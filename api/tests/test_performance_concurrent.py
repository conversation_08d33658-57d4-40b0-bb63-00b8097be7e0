"""
Performance and concurrency tests for the memory system.

This module tests the system's performance characteristics:
- High-concurrency memory operations
- Queue processing under load
- Memory usage and resource management
- Response time benchmarks
- Thread safety under stress
"""

import pytest
import time
import threading
import psutil
import os
from unittest.mock import patch, Mock
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor, as_completed

from app.utils.memory import Memory<PERSON>lient<PERSON>ing<PERSON>, reset_memory_client
from tests.utils.test_helpers import MockVectorStore, wait_for_condition


class TestHighConcurrencyOperations:
    """Test high-concurrency memory operations."""
    
    @pytest.mark.slow
    def test_high_volume_concurrent_operations(self, memory_singleton, patched_memory_client, test_config):
        """Test system behavior under high-volume concurrent operations."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Initialize client
        client = memory_singleton.get_client(test_config)
        assert client is not None, "Client should be initialized"
        
        # Track performance metrics
        start_time = time.time()
        operation_count = 100
        max_workers = 20
        
        results = []
        errors = []
        
        def add_memory_operation(index):
            try:
                content = f"High volume test memory {index}"
                metadata = {"test": "high_volume", "index": index, "timestamp": time.time()}
                
                operation_start = time.time()
                success, message = memory_singleton.add_memory_async(content, metadata)
                operation_time = time.time() - operation_start
                
                return {
                    "success": success,
                    "message": message,
                    "operation_time": operation_time,
                    "index": index
                }
            except Exception as e:
                errors.append({"index": index, "error": str(e)})
                return None
        
        # Execute concurrent operations
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(add_memory_operation, i) for i in range(operation_count)]
            
            for future in as_completed(futures):
                result = future.result()
                if result:
                    results.append(result)
        
        # Wait for all async operations to complete
        assert memory_singleton.wait_for_queue_empty(timeout=30.0), "All operations should complete"
        
        total_time = time.time() - start_time
        
        # Verify results
        assert len(errors) == 0, f"No errors should occur: {errors}"
        assert len(results) == operation_count, "All operations should complete"
        assert all(r["success"] for r in results), "All operations should succeed"
        
        # Performance assertions
        avg_operation_time = sum(r["operation_time"] for r in results) / len(results)
        operations_per_second = operation_count / total_time
        
        print(f"Performance metrics:")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Average operation time: {avg_operation_time:.4f}s")
        print(f"  Operations per second: {operations_per_second:.2f}")
        
        # Basic performance expectations
        assert avg_operation_time < 0.1, "Average operation time should be reasonable"
        assert operations_per_second > 10, "Should handle at least 10 operations per second"
        
        # Verify all memories were stored
        assert len(mock_vector_store.stored_memories) == operation_count, "All memories should be stored"
    
    @pytest.mark.slow
    def test_queue_processing_under_load(self, memory_singleton, patched_memory_client, test_config):
        """Test queue processing behavior under sustained load."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Add artificial delay to simulate real-world processing time
        original_add = mock_vector_store.add
        def slow_add(*args, **kwargs):
            time.sleep(0.01)  # 10ms delay per operation
            return original_add(*args, **kwargs)
        
        mock_vector_store.add = slow_add
        
        # Initialize client
        client = memory_singleton.get_client(test_config)
        
        # Add operations in batches to test queue behavior
        batch_size = 25
        num_batches = 4
        total_operations = batch_size * num_batches
        
        batch_times = []
        
        for batch in range(num_batches):
            batch_start = time.time()
            
            # Add batch of operations
            for i in range(batch_size):
                operation_index = batch * batch_size + i
                success, message = memory_singleton.add_memory_async(
                    f"Load test batch {batch} item {i}",
                    {"batch": batch, "item": i, "operation_index": operation_index}
                )
                assert success, f"Operation should be queued: {message}"
            
            # Monitor queue status during processing
            queue_status = memory_singleton.get_operation_queue_status()
            print(f"Batch {batch}: Queue size = {queue_status['queue_size']}, Worker running = {queue_status['worker_running']}")
            
            batch_time = time.time() - batch_start
            batch_times.append(batch_time)
        
        # Wait for all operations to complete
        assert memory_singleton.wait_for_queue_empty(timeout=60.0), "All operations should complete"
        
        # Verify final state
        final_status = memory_singleton.get_operation_queue_status()
        assert final_status["queue_size"] == 0, "Queue should be empty"
        assert final_status["operation_counter"] >= total_operations, "All operations should be counted"
        
        # Verify all memories were processed
        assert len(mock_vector_store.stored_memories) == total_operations, "All memories should be stored"
        
        print(f"Queue processing metrics:")
        print(f"  Total operations: {total_operations}")
        print(f"  Average batch time: {sum(batch_times) / len(batch_times):.2f}s")
        print(f"  Final operation counter: {final_status['operation_counter']}")
    
    def test_thread_safety_under_stress(self, memory_singleton, test_config, concurrency_helper):
        """Test thread safety under stress conditions."""
        with patch('app.utils.memory.Memory') as mock_memory_class:
            mock_client = Mock()
            
            # Add thread-safety verification to mock
            call_log = []
            call_lock = threading.Lock()
            
            def thread_safe_add(content, metadata=None):
                with call_lock:
                    call_log.append({
                        "thread_id": threading.current_thread().ident,
                        "content": content,
                        "timestamp": time.time()
                    })
                
                # Simulate some processing time
                time.sleep(0.001)
                return {"id": f"thread-safe-{len(call_log)}", "status": "success"}
            
            mock_client.add = thread_safe_add
            mock_memory_class.from_config.return_value = mock_client
            
            # Initialize client
            client = memory_singleton.get_client(test_config)
            
            # Define stress operation
            def stress_operation(index):
                # Mix of different operations to stress different code paths
                operations = []
                
                # Add memory
                success, message = memory_singleton.add_memory_async(f"Stress test {index}")
                operations.append(("add", success, message))
                
                # Check queue status
                status = memory_singleton.get_operation_queue_status()
                operations.append(("status", True, status))
                
                # Check health
                health = memory_singleton.is_healthy()
                operations.append(("health", health, "health_check"))
                
                return operations
            
            # Run stress test
            args_list = [(i,) for i in range(50)]
            results = concurrency_helper.run_concurrent_operations(
                stress_operation, args_list, max_workers=15
            )
            
            # Wait for async operations to complete
            assert memory_singleton.wait_for_queue_empty(timeout=30.0)
            
            # Verify thread safety
            assert results["error_count"] == 0, f"No errors should occur: {results['errors']}"
            assert len(results["results"]) == 50, "All stress operations should complete"
            
            # Verify all add operations were successful
            add_operations = []
            for result_set in results["results"]:
                for op_type, success, message in result_set:
                    if op_type == "add":
                        add_operations.append((success, message))
            
            assert all(success for success, _ in add_operations), "All add operations should succeed"
            
            # Verify call log integrity (no race conditions)
            assert len(call_log) == 50, "All calls should be logged"
            
            # Check for thread diversity (operations from multiple threads)
            thread_ids = set(call["thread_id"] for call in call_log)
            assert len(thread_ids) > 1, "Operations should come from multiple threads"


class TestMemoryUsageAndResourceManagement:
    """Test memory usage and resource management."""
    
    def test_memory_usage_under_load(self, memory_singleton, patched_memory_client, test_config):
        """Test memory usage doesn't grow excessively under load."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Initialize client
        client = memory_singleton.get_client(test_config)
        
        # Perform many operations
        operation_count = 200
        for i in range(operation_count):
            success, message = memory_singleton.add_memory_async(
                f"Memory usage test {i}",
                {"test": "memory_usage", "index": i}
            )
            assert success, f"Operation {i} should succeed"
            
            # Periodically check memory usage
            if i % 50 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_growth = current_memory - initial_memory
                print(f"Operation {i}: Memory usage = {current_memory:.1f}MB (growth: {memory_growth:.1f}MB)")
        
        # Wait for all operations to complete
        assert memory_singleton.wait_for_queue_empty(timeout=30.0)
        
        # Check final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_growth = final_memory - initial_memory
        
        print(f"Memory usage summary:")
        print(f"  Initial: {initial_memory:.1f}MB")
        print(f"  Final: {final_memory:.1f}MB")
        print(f"  Growth: {memory_growth:.1f}MB")
        
        # Memory growth should be reasonable (less than 50MB for this test)
        assert memory_growth < 50, f"Memory growth should be reasonable, got {memory_growth:.1f}MB"
    
    def test_queue_size_management(self, memory_singleton, patched_memory_client, test_config):
        """Test that queue size is properly managed."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Add delay to create queue buildup
        original_add = mock_vector_store.add
        def delayed_add(*args, **kwargs):
            time.sleep(0.05)  # 50ms delay
            return original_add(*args, **kwargs)
        
        mock_vector_store.add = delayed_add
        
        # Initialize client
        client = memory_singleton.get_client(test_config)
        
        # Add operations faster than they can be processed
        max_queue_size = 0
        for i in range(20):
            success, message = memory_singleton.add_memory_async(f"Queue test {i}")
            assert success, f"Operation {i} should be queued"
            
            # Monitor queue size
            status = memory_singleton.get_operation_queue_status()
            current_queue_size = status["queue_size"]
            max_queue_size = max(max_queue_size, current_queue_size)
            
            time.sleep(0.01)  # Small delay between additions
        
        print(f"Maximum queue size observed: {max_queue_size}")
        
        # Wait for queue to empty
        assert memory_singleton.wait_for_queue_empty(timeout=30.0)
        
        # Verify final state
        final_status = memory_singleton.get_operation_queue_status()
        assert final_status["queue_size"] == 0, "Queue should be empty"
        assert len(mock_vector_store.stored_memories) == 20, "All operations should complete"


class TestResponseTimeBenchmarks:
    """Test response time benchmarks."""
    
    def test_operation_response_times(self, memory_singleton, patched_memory_client, test_config):
        """Test response times for different operations."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Initialize client
        client = memory_singleton.get_client(test_config)
        
        # Test different operation types
        operations = {
            "add_memory": lambda: memory_singleton.add_memory_async("Response time test"),
            "queue_status": lambda: memory_singleton.get_operation_queue_status(),
            "health_check": lambda: memory_singleton.is_healthy(),
        }
        
        response_times = {}
        
        for op_name, op_func in operations.items():
            times = []
            
            # Run each operation multiple times
            for _ in range(10):
                start_time = time.time()
                result = op_func()
                end_time = time.time()
                
                times.append(end_time - start_time)
                
                # Verify operation succeeded (where applicable)
                if op_name == "add_memory":
                    assert result[0], f"Add memory should succeed: {result[1]}"
                elif op_name == "health_check":
                    assert isinstance(result, bool), "Health check should return boolean"
                elif op_name == "queue_status":
                    assert isinstance(result, dict), "Queue status should return dict"
            
            response_times[op_name] = {
                "min": min(times),
                "max": max(times),
                "avg": sum(times) / len(times),
                "times": times
            }
        
        # Wait for async operations to complete
        memory_singleton.wait_for_queue_empty(timeout=10.0)
        
        # Print benchmark results
        print("Response time benchmarks:")
        for op_name, metrics in response_times.items():
            print(f"  {op_name}:")
            print(f"    Min: {metrics['min']:.4f}s")
            print(f"    Max: {metrics['max']:.4f}s")
            print(f"    Avg: {metrics['avg']:.4f}s")
        
        # Basic performance expectations
        assert response_times["add_memory"]["avg"] < 0.01, "Add memory should be fast (async)"
        assert response_times["queue_status"]["avg"] < 0.001, "Queue status should be very fast"
        assert response_times["health_check"]["avg"] < 0.001, "Health check should be very fast"
    
    @pytest.mark.slow
    def test_sustained_load_performance(self, memory_singleton, patched_memory_client, test_config):
        """Test performance under sustained load over time."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Initialize client
        client = memory_singleton.get_client(test_config)
        
        # Run sustained load test
        duration = 10  # seconds
        start_time = time.time()
        operation_count = 0
        response_times = []
        
        while time.time() - start_time < duration:
            op_start = time.time()
            success, message = memory_singleton.add_memory_async(f"Sustained load {operation_count}")
            op_end = time.time()
            
            assert success, f"Operation should succeed: {message}"
            response_times.append(op_end - op_start)
            operation_count += 1
            
            # Small delay to prevent overwhelming
            time.sleep(0.01)
        
        # Wait for all operations to complete
        assert memory_singleton.wait_for_queue_empty(timeout=30.0)
        
        total_time = time.time() - start_time
        avg_response_time = sum(response_times) / len(response_times)
        operations_per_second = operation_count / total_time
        
        print(f"Sustained load performance:")
        print(f"  Duration: {total_time:.2f}s")
        print(f"  Total operations: {operation_count}")
        print(f"  Operations per second: {operations_per_second:.2f}")
        print(f"  Average response time: {avg_response_time:.4f}s")
        
        # Performance expectations for sustained load
        assert operations_per_second > 50, "Should maintain reasonable throughput"
        assert avg_response_time < 0.01, "Response times should remain low"
        assert len(mock_vector_store.stored_memories) == operation_count, "All operations should complete"
