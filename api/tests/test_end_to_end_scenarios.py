"""
End-to-end integration tests for the memory system API.

This module tests the complete API functionality including:
- REST API endpoints
- MCP server functionality  
- Authentication and authorization
- Memory chunking for large content
- Metadata handling and search operations
"""

import pytest
import json
import uuid
import time
from unittest.mock import patch, Mock
from fastapi.testclient import TestClient

from app.main import app
from app.utils.memory import Memory<PERSON>lient<PERSON>ingleton, reset_memory_client
from tests.utils.test_helpers import mock_memory_client, create_test_user_and_app


class TestRestAPIEndpoints:
    """Test REST API endpoints end-to-end."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        """Create authentication headers."""
        return {"Authorization": "Bearer test-token"}
    
    def test_create_memory_endpoint(self, client, patched_memory_client, test_user_and_app):
        """Test memory creation through REST API."""
        mock_client, mock_vector_store = patched_memory_client
        user, app = test_user_and_app
        
        # Prepare request data
        memory_data = {
            "text": "Test memory via REST API",
            "app": app.name
        }
        
        # Mock the memory client response
        mock_vector_store.add.return_value = {
            "id": str(uuid.uuid4()),
            "status": "success"
        }
        
        # Make request
        response = client.post("/memories/", json=memory_data)
        
        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert "id" in response_data
        assert response_data["status"] == "success"
        
        # Verify memory was stored
        assert len(mock_vector_store.stored_memories) == 1
    
    def test_list_memories_endpoint(self, client, patched_memory_client, test_user_and_app):
        """Test memory listing through REST API."""
        mock_client, mock_vector_store = patched_memory_client
        user, app = test_user_and_app
        
        # Add some test memories
        test_memories = [
            {"content": "Memory 1", "metadata": {"index": 1}},
            {"content": "Memory 2", "metadata": {"index": 2}},
            {"content": "Memory 3", "metadata": {"index": 3}}
        ]
        
        for memory in test_memories:
            mock_vector_store.add(memory["content"], memory["metadata"])
        
        # Mock the get_all response
        mock_client.get_all.return_value = {
            "results": list(mock_vector_store.stored_memories.values())
        }
        
        # Make request
        response = client.get(f"/memories/?app_id={app.id}")
        
        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert "items" in response_data
        assert len(response_data["items"]) == 3
    
    def test_search_memories_endpoint(self, client, patched_memory_client, test_user_and_app):
        """Test memory search through REST API."""
        mock_client, mock_vector_store = patched_memory_client
        user, app = test_user_and_app
        
        # Add searchable memories
        mock_vector_store.add("Python programming tutorial", {"topic": "programming"})
        mock_vector_store.add("JavaScript web development", {"topic": "web"})
        mock_vector_store.add("Python data science", {"topic": "data"})
        
        # Make search request
        search_params = {
            "query": "Python",
            "app_id": str(app.id),
            "limit": 10
        }
        
        response = client.get("/memories/search", params=search_params)
        
        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert "results" in response_data
        
        # Should find Python-related memories
        results = response_data["results"]
        assert len(results) >= 2  # Should find both Python memories
    
    def test_memory_chunking_endpoint(self, client, patched_memory_client, 
                                    test_user_and_app, large_memory_content):
        """Test automatic memory chunking for large content."""
        mock_client, mock_vector_store = patched_memory_client
        user, app = test_user_and_app
        
        # Prepare large content request
        memory_data = {
            "text": large_memory_content,
            "app": app.name
        }
        
        # Mock chunking response
        chunk_responses = []
        for i in range(3):  # Simulate 3 chunks
            chunk_responses.append({
                "id": str(uuid.uuid4()),
                "status": "success"
            })
        
        mock_vector_store.add.side_effect = chunk_responses
        
        # Make request
        response = client.post("/memories/", json=memory_data)
        
        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert "chunks_created" in response_data or response_data["status"] == "success"
        
        # Verify multiple chunks were created
        assert mock_vector_store.call_count >= 3, "Should create multiple chunks"
    
    def test_memory_metadata_handling(self, client, patched_memory_client, test_user_and_app):
        """Test metadata handling through REST API."""
        mock_client, mock_vector_store = patched_memory_client
        user, app = test_user_and_app
        
        # Prepare request with metadata
        memory_data = {
            "text": "Memory with metadata",
            "app": app.name,
            "metadata": {
                "category": "test",
                "priority": "high",
                "tags": ["api", "test", "metadata"]
            }
        }
        
        # Make request
        response = client.post("/memories/", json=memory_data)
        
        # Verify response
        assert response.status_code == 200
        
        # Verify metadata was preserved
        stored_memory = list(mock_vector_store.stored_memories.values())[0]
        assert "source_app" in stored_memory["metadata"]
        assert stored_memory["metadata"]["source_app"] == "openmemory"


class TestMCPServerEndpoints:
    """Test MCP server functionality end-to-end."""
    
    @pytest.fixture
    def mcp_client(self):
        """Create MCP test client."""
        from app.mcp_server import mcp
        return TestClient(mcp.app)
    
    def test_mcp_add_memory(self, mcp_client, patched_memory_client):
        """Test adding memory through MCP server."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Prepare MCP request
        mcp_request = {
            "method": "add_memory",
            "params": {
                "content": "MCP test memory",
                "user_id": "test-user",
                "metadata": {"source": "mcp_test"}
            }
        }
        
        # Make MCP request
        response = mcp_client.post("/mcp", json=mcp_request)
        
        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert "result" in response_data
        assert response_data["result"]["status"] == "success"
    
    def test_mcp_search_memory(self, mcp_client, patched_memory_client):
        """Test searching memory through MCP server."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Add test memory
        mock_vector_store.add("MCP searchable content", {"test": True})
        
        # Prepare MCP search request
        mcp_request = {
            "method": "search_memories",
            "params": {
                "query": "searchable",
                "user_id": "test-user",
                "limit": 5
            }
        }
        
        # Make MCP request
        response = mcp_client.post("/mcp", json=mcp_request)
        
        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert "result" in response_data
        assert len(response_data["result"]["results"]) > 0
    
    def test_mcp_get_all_memories(self, mcp_client, patched_memory_client):
        """Test getting all memories through MCP server."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Add multiple test memories
        for i in range(5):
            mock_vector_store.add(f"MCP memory {i}", {"index": i})
        
        # Prepare MCP request
        mcp_request = {
            "method": "get_all_memories",
            "params": {
                "user_id": "test-user"
            }
        }
        
        # Make MCP request
        response = mcp_client.post("/mcp", json=mcp_request)
        
        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert "result" in response_data
        assert len(response_data["result"]["results"]) == 5


class TestAuthenticationIntegration:
    """Test authentication and authorization integration."""
    
    def test_unauthorized_access(self, client):
        """Test that unauthorized requests are rejected."""
        # Try to access protected endpoint without auth
        response = client.get("/memories/")
        
        # Should require authentication
        # Note: Actual behavior depends on your auth implementation
        # This test may need adjustment based on your auth setup
        assert response.status_code in [401, 403, 200]  # Adjust based on your auth
    
    def test_invalid_app_access(self, client, test_user_and_app):
        """Test access control for invalid app."""
        user, app = test_user_and_app
        
        # Try to access memories for non-existent app
        invalid_app_id = str(uuid.uuid4())
        response = client.get(f"/memories/?app_id={invalid_app_id}")
        
        # Should handle invalid app gracefully
        assert response.status_code in [404, 403, 200]  # Adjust based on your implementation


class TestErrorHandlingIntegration:
    """Test error handling in end-to-end scenarios."""
    
    def test_invalid_memory_data(self, client):
        """Test handling of invalid memory data."""
        # Send invalid data
        invalid_data = {
            "text": "",  # Empty text
            "app": ""    # Empty app
        }
        
        response = client.post("/memories/", json=invalid_data)
        
        # Should return appropriate error
        assert response.status_code in [400, 422]  # Bad request or validation error
    
    def test_vector_store_failure_handling(self, client, patched_failing_memory_client, test_user_and_app):
        """Test handling of vector store failures."""
        mock_client, failing_vector_store = patched_failing_memory_client
        user, app = test_user_and_app
        
        # Mock database fallback
        with patch('app.utils.memory.MemoryClientSingleton._store_in_database_only') as mock_db:
            mock_db.return_value = {"id": "fallback-id", "status": "success", "fallback_mode": True}
            
            memory_data = {
                "text": "Test memory with vector store failure",
                "app": app.name
            }
            
            response = client.post("/memories/", json=memory_data)
            
            # Should still succeed with fallback
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["status"] == "success"
    
    def test_timeout_handling(self, client, timeout_vector_store, test_user_and_app):
        """Test handling of operation timeouts."""
        user, app = test_user_and_app
        
        with patch('app.utils.memory.Memory') as mock_memory_class:
            mock_client = Mock()
            mock_client.add = timeout_vector_store.add
            mock_memory_class.from_config.return_value = mock_client
            
            memory_data = {
                "text": "Test memory with timeout",
                "app": app.name
            }
            
            response = client.post("/memories/", json=memory_data)
            
            # Should handle timeout gracefully
            assert response.status_code in [200, 408, 500]  # Success, timeout, or server error
