"""
Test suite for concurrent operation handling in MemoryClientSingleton.

This module tests the concurrent operation functionality added in Task 6,
including operation queuing, worker thread management, and async operations.
"""

import unittest
import threading
import time
import queue
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.utils.memory import MemoryClientSingleton


class TestConcurrentOperations(unittest.TestCase):
    """Test concurrent operation handling in MemoryClientSingleton."""
    
    def setUp(self):
        """Set up test environment before each test."""
        # Reset singleton instance for clean testing
        MemoryClientSingleton._instance = None
        MemoryClientSingleton._client = None
        MemoryClientSingleton._config_hash = None
        MemoryClientSingleton._last_health_check = None
        MemoryClientSingleton._worker_thread = None
        MemoryClientSingleton._worker_running = False
        MemoryClientSingleton._operation_counter = 0
        
        # Clear the operation queue
        while not MemoryClientSingleton._operation_queue.empty():
            try:
                MemoryClientSingleton._operation_queue.get_nowait()
            except queue.Empty:
                break
    
    def tearDown(self):
        """Clean up after each test."""
        # Stop any running worker threads
        singleton = MemoryClientSingleton()
        singleton._stop_worker()
        
        # Wait a bit for threads to stop
        time.sleep(0.1)
    
    def test_singleton_pattern_with_concurrent_access(self):
        """Test that singleton pattern works correctly with concurrent access."""
        instances = []
        errors = []
        
        def get_instance():
            try:
                instances.append(MemoryClientSingleton())
            except Exception as e:
                errors.append(str(e))
        
        # Create multiple threads that try to get the singleton instance
        threads = [threading.Thread(target=get_instance) for _ in range(10)]
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify no errors occurred
        self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
        
        # Verify all instances are the same object
        self.assertEqual(len(instances), 10, "Should have 10 instances")
        self.assertEqual(len(set(id(instance) for instance in instances)), 1, 
                        "All instances should be identical")
    
    @patch('app.utils.memory.Memory')
    def test_worker_thread_lifecycle(self, mock_memory_class):
        """Test worker thread start and stop functionality."""
        # Mock the memory client
        mock_client = Mock()
        mock_memory_class.from_config.return_value = mock_client
        
        singleton = MemoryClientSingleton()
        
        # Initialize client with mock config
        config = {"test": "config"}
        singleton.get_client(config)
        
        # Initially no worker thread should be running
        self.assertFalse(singleton._worker_running)
        self.assertIsNone(singleton._worker_thread)
        
        # Start worker thread
        singleton._start_worker()
        
        # Verify worker thread is running
        self.assertTrue(singleton._worker_running)
        self.assertIsNotNone(singleton._worker_thread)
        self.assertTrue(singleton._worker_thread.is_alive())
        
        # Stop worker thread
        singleton._stop_worker()
        
        # Wait for thread to stop
        time.sleep(0.2)
        
        # Verify worker thread is stopped
        self.assertFalse(singleton._worker_running)
    
    @patch('app.utils.memory.Memory')
    def test_add_memory_async(self, mock_memory_class):
        """Test asynchronous memory addition."""
        # Mock the memory client
        mock_client = Mock()
        mock_client.add.return_value = {"id": "test-id", "status": "success"}
        mock_memory_class.from_config.return_value = mock_client
        
        singleton = MemoryClientSingleton()
        
        # Initialize client
        config = {"test": "config"}
        singleton.get_client(config)
        
        # Test async memory addition
        success, message = singleton.add_memory_async("test content", {"test": "metadata"})
        
        self.assertTrue(success, f"Operation should succeed: {message}")
        self.assertIn("queued", message.lower())
        
        # Wait for operation to complete
        self.assertTrue(singleton.wait_for_queue_empty(timeout=5.0))
        
        # Verify the mock was called
        mock_client.add.assert_called_once_with("test content", metadata={"test": "metadata"})
    
    @patch('app.utils.memory.Memory')
    def test_concurrent_memory_operations(self, mock_memory_class):
        """Test multiple concurrent memory operations."""
        # Mock the memory client
        mock_client = Mock()
        mock_client.add.return_value = {"id": "test-id", "status": "success"}
        mock_memory_class.from_config.return_value = mock_client
        
        singleton = MemoryClientSingleton()
        
        # Initialize client
        config = {"test": "config"}
        singleton.get_client(config)
        
        # Track results
        results = []
        errors = []
        
        def callback(success, result, operation_id):
            if success:
                results.append((operation_id, result))
            else:
                errors.append((operation_id, result))
        
        # Queue multiple operations concurrently
        num_operations = 20
        for i in range(num_operations):
            success, message = singleton.add_memory_async(
                f"test content {i}", 
                {"index": i}, 
                callback
            )
            self.assertTrue(success, f"Operation {i} should be queued successfully")
        
        # Wait for all operations to complete
        self.assertTrue(singleton.wait_for_queue_empty(timeout=10.0))
        
        # Wait a bit more for callbacks to complete
        time.sleep(0.5)
        
        # Verify all operations completed successfully
        self.assertEqual(len(errors), 0, f"No errors should occur: {errors}")
        self.assertEqual(len(results), num_operations, 
                        f"All {num_operations} operations should complete")
        
        # Verify mock was called the correct number of times
        self.assertEqual(mock_client.add.call_count, num_operations)
    
    @patch('app.utils.memory.Memory')
    def test_operation_queue_status(self, mock_memory_class):
        """Test operation queue status reporting."""
        # Mock the memory client
        mock_client = Mock()
        mock_memory_class.from_config.return_value = mock_client
        
        singleton = MemoryClientSingleton()
        
        # Initialize client
        config = {"test": "config"}
        singleton.get_client(config)
        
        # Get initial status
        status = singleton.get_operation_queue_status()
        
        self.assertIn("queue_size", status)
        self.assertIn("worker_running", status)
        self.assertIn("worker_alive", status)
        self.assertIn("operation_counter", status)
        
        # Initially queue should be empty and worker not running
        self.assertEqual(status["queue_size"], 0)
        self.assertFalse(status["worker_running"])
        self.assertFalse(status["worker_alive"])
        self.assertEqual(status["operation_counter"], 0)
        
        # Queue an operation
        singleton.add_memory_async("test content")
        
        # Check status after queuing
        status = singleton.get_operation_queue_status()
        self.assertTrue(status["worker_running"])
        self.assertTrue(status["worker_alive"])
    
    @patch('app.utils.memory.Memory')
    def test_error_handling_in_async_operations(self, mock_memory_class):
        """Test error handling in asynchronous operations."""
        # Mock the memory client to raise an exception
        mock_client = Mock()
        mock_client.add.side_effect = Exception("Test error")
        mock_memory_class.from_config.return_value = mock_client
        
        singleton = MemoryClientSingleton()
        
        # Initialize client
        config = {"test": "config"}
        singleton.get_client(config)
        
        # Track callback results
        callback_results = []
        
        def error_callback(success, result, operation_id):
            callback_results.append((success, result, operation_id))
        
        # Queue operation that will fail
        success, message = singleton.add_memory_async("test content", callback=error_callback)
        self.assertTrue(success, "Queuing should succeed even if operation will fail")
        
        # Wait for operation to complete
        self.assertTrue(singleton.wait_for_queue_empty(timeout=5.0))
        
        # Wait for callback
        time.sleep(0.5)
        
        # Verify error was handled properly
        self.assertEqual(len(callback_results), 1)
        success, result, operation_id = callback_results[0]
        self.assertFalse(success, "Operation should have failed")
        self.assertIn("Test error", result)
        self.assertIsInstance(operation_id, int)


    @patch('app.utils.memory.Memory')
    def test_search_memory_async(self, mock_memory_class):
        """Test asynchronous memory search."""
        # Mock the memory client
        mock_client = Mock()
        mock_client.search.return_value = [{"id": "test-id", "content": "test result"}]
        mock_memory_class.from_config.return_value = mock_client

        singleton = MemoryClientSingleton()

        # Initialize client
        config = {"test": "config"}
        singleton.get_client(config)

        # Test async memory search
        success, message = singleton.search_memory_async("test query", limit=5)

        self.assertTrue(success, f"Search operation should succeed: {message}")
        self.assertIn("queued", message.lower())

        # Wait for operation to complete
        self.assertTrue(singleton.wait_for_queue_empty(timeout=5.0))

        # Verify the mock was called
        mock_client.search.assert_called_once_with("test query", limit=5)

    def test_async_operations_without_client(self):
        """Test async operations when client is not initialized."""
        singleton = MemoryClientSingleton()

        # Try async operation without initializing client
        success, message = singleton.add_memory_async("test content")

        self.assertFalse(success, "Operation should fail without initialized client")
        self.assertIn("not initialized", message.lower())

    @patch('app.utils.memory.Memory')
    def test_reset_client_stops_worker(self, mock_memory_class):
        """Test that resetting client properly stops worker thread."""
        # Mock the memory client
        mock_client = Mock()
        mock_memory_class.from_config.return_value = mock_client

        singleton = MemoryClientSingleton()

        # Initialize client and start worker
        config = {"test": "config"}
        singleton.get_client(config)
        singleton._start_worker()

        # Verify worker is running
        self.assertTrue(singleton._worker_running)

        # Reset client
        singleton.reset_client()

        # Wait for worker to stop
        time.sleep(0.2)

        # Verify worker is stopped
        self.assertFalse(singleton._worker_running)
        self.assertIsNone(singleton._client)

    @patch('app.utils.memory.Memory')
    def test_stress_test_concurrent_operations(self, mock_memory_class):
        """Stress test with high volume of concurrent operations."""
        # Mock the memory client with slight delay to simulate real operations
        mock_client = Mock()
        def slow_add(*args, **kwargs):
            time.sleep(0.01)  # Small delay to simulate processing
            return {"id": f"test-{time.time()}", "status": "success"}

        mock_client.add.side_effect = slow_add
        mock_memory_class.from_config.return_value = mock_client

        singleton = MemoryClientSingleton()

        # Initialize client
        config = {"test": "config"}
        singleton.get_client(config)

        # Track results
        completed_operations = []
        failed_operations = []

        def result_callback(success, result, operation_id):
            if success:
                completed_operations.append(operation_id)
            else:
                failed_operations.append((operation_id, result))

        # Queue many operations
        num_operations = 100
        start_time = time.time()

        for i in range(num_operations):
            success, message = singleton.add_memory_async(
                f"stress test content {i}",
                {"stress_test": True, "index": i},
                result_callback
            )
            self.assertTrue(success, f"Operation {i} should be queued")

        # Wait for all operations to complete
        self.assertTrue(singleton.wait_for_queue_empty(timeout=30.0))

        # Wait for callbacks to complete
        time.sleep(1.0)

        end_time = time.time()
        duration = end_time - start_time

        # Verify all operations completed successfully
        self.assertEqual(len(failed_operations), 0,
                        f"No operations should fail: {failed_operations}")
        self.assertEqual(len(completed_operations), num_operations,
                        f"All {num_operations} operations should complete")

        # Verify performance (should complete in reasonable time)
        self.assertLess(duration, 20.0,
                       f"Operations took too long: {duration:.2f}s")

        print(f"Stress test completed: {num_operations} operations in {duration:.2f}s")


if __name__ == '__main__':
    unittest.main()
