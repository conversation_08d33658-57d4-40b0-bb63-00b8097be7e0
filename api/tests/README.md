# Memory System - Comprehensive Integration Test Suite

This directory contains a comprehensive integration test suite for the Memory Master v2 memory system. The test suite validates the reliability, performance, and correctness of the enhanced memory system implementation.

## Test Structure

### Test Categories

1. **Integration Tests** (`test_comprehensive_integration.py`)
   - Complete memory lifecycle testing
   - Singleton pattern validation
   - Configuration hot-reload integration
   - Health check integration
   - Concurrent operation consistency

2. **End-to-End API Tests** (`test_end_to_end_scenarios.py`)
   - REST API endpoint testing
   - MCP server functionality
   - Authentication and authorization
   - Memory chunking for large content
   - Metadata handling and search operations

3. **Reliability Tests** (`test_reliability_scenarios.py`)
   - Graceful degradation scenarios
   - Recovery from degraded mode
   - Retry logic validation
   - Backlog processing
   - Error handling and logging

4. **Performance Tests** (`test_performance_concurrent.py`)
   - High-concurrency memory operations
   - Queue processing under load
   - Memory usage monitoring
   - Response time benchmarks
   - Thread safety under stress

### Test Utilities

- **`utils/test_helpers.py`** - Common test utilities and helpers
- **`conftest.py`** - Pytest configuration and shared fixtures

## Running Tests

### Quick Start

```bash
# Run all tests
./run_integration_tests.py

# Run specific test category
./run_integration_tests.py --category integration
./run_integration_tests.py --category reliability
./run_integration_tests.py --category performance
./run_integration_tests.py --category api

# Run tests with coverage
./run_integration_tests.py --coverage --html-report

# Run tests in parallel (faster)
./run_integration_tests.py --parallel 4

# Skip slow tests
./run_integration_tests.py --fast
```

### Manual Pytest Commands

```bash
# Run all integration tests
pytest tests/ -v

# Run specific test file
pytest tests/test_comprehensive_integration.py -v

# Run tests with specific markers
pytest -m "integration" -v
pytest -m "reliability" -v
pytest -m "concurrent" -v

# Run with coverage
pytest --cov=app --cov-report=html tests/

# Run in parallel
pytest -n 4 tests/
```

## Test Markers

The test suite uses pytest markers to categorize tests:

- `@pytest.mark.integration` - Core integration tests
- `@pytest.mark.slow` - Tests that take longer to run
- `@pytest.mark.concurrent` - Concurrency and thread safety tests
- `@pytest.mark.reliability` - Reliability and failure scenario tests

## Test Fixtures

### Core Fixtures

- `reset_singletons` - Resets all singletons before each test
- `test_db` - Provides a clean test database
- `test_user_and_app` - Creates test user and app
- `memory_singleton` - Provides a fresh memory client singleton
- `config_manager` - Provides a config manager instance

### Mock Fixtures

- `mock_vector_store` - Mock vector store for controlled testing
- `failing_vector_store` - Mock vector store that fails operations
- `timeout_vector_store` - Mock vector store that times out
- `patched_memory_client` - Patched memory client with mock vector store

### Configuration Fixtures

- `test_config` - Standard test configuration
- `test_config_no_vector` - Configuration without vector store
- `non_critical_config_change` - Non-critical configuration change
- `critical_config_change` - Critical configuration change requiring restart

### Utility Fixtures

- `concurrency_helper` - Helper for concurrent operation testing
- `test_memory_data` - Sample memory data for testing
- `large_memory_content` - Large content for chunking tests

## Test Environment Setup

### Prerequisites

```bash
# Install test dependencies
pip install pytest pytest-cov pytest-html pytest-xdist psutil
```

### Environment Variables

The test suite uses these environment variables:

- `TESTING=true` - Indicates test environment
- `DATABASE_URL` - Test database URL (defaults to SQLite)
- `LOG_LEVEL=INFO` - Logging level for tests

## Test Data and Cleanup

- Tests use temporary databases that are automatically cleaned up
- Mock vector stores are reset between tests
- Singletons are reset before each test to ensure isolation
- No persistent test data is left behind

## Performance Benchmarks

The performance tests establish baseline expectations:

- **Response Times**: Operations should complete within reasonable time limits
- **Throughput**: System should handle minimum operations per second
- **Memory Usage**: Memory growth should be bounded under load
- **Concurrency**: System should handle concurrent operations safely

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you're running tests from the `api/` directory
2. **Database Errors**: Check that test database permissions are correct
3. **Timeout Errors**: Increase timeout values for slower systems
4. **Memory Errors**: Reduce concurrency levels for resource-constrained systems

### Debug Mode

```bash
# Run with verbose output and no capture
pytest tests/ -v -s

# Run single test with debugging
pytest tests/test_comprehensive_integration.py::TestMemorySystemIntegration::test_singleton_pattern_thread_safety -v -s

# Run with pdb on failures
pytest tests/ --pdb
```

### Logging

Tests use structured logging. To see detailed logs:

```bash
# Run with log output
pytest tests/ -v --log-cli-level=INFO

# Run with debug logging
pytest tests/ -v --log-cli-level=DEBUG
```

## Continuous Integration

The test suite is designed to work in CI environments:

```bash
# CI-friendly command
./run_integration_tests.py --junit-xml=test-results.xml --coverage --fast
```

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Use appropriate fixtures and markers
3. Include both positive and negative test cases
4. Add performance considerations for resource-intensive tests
5. Update this README if adding new test categories

## Test Coverage

The test suite aims for comprehensive coverage of:

- ✅ Singleton pattern implementation
- ✅ Thread safety and concurrency
- ✅ Configuration management and hot-reload
- ✅ Memory operations (add, get, search, delete)
- ✅ Graceful degradation and recovery
- ✅ Error handling and retry logic
- ✅ API endpoints and MCP server
- ✅ Performance under load
- ✅ Resource management

## Reporting Issues

If tests fail unexpectedly:

1. Check the test output for specific error messages
2. Verify your environment meets the prerequisites
3. Run tests individually to isolate issues
4. Check system resources (memory, disk space)
5. Review recent changes to the codebase
