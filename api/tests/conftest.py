"""
Pytest configuration and shared fixtures for integration tests.

This module provides common fixtures and configuration for the comprehensive
integration test suite.
"""

import pytest
import os
import sys
import tempfile
import shutil
from unittest.mock import patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.database import Base, SessionLocal
from app.utils.memory import reset_memory_client, MemoryClientSingleton
from app.utils.config_manager import ConfigManager
from tests.utils.test_helpers import (
    TestDatabaseManager, MockVectorStore, TestConfigManager,
    ConcurrencyTestHelper, setup_test_logging, create_test_user_and_app
)


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Set up the test environment once for the entire test session."""
    setup_test_logging()
    
    # Set test environment variables
    os.environ['TESTING'] = 'true'
    os.environ['DATABASE_URL'] = 'sqlite:///test_memory.db'
    
    yield
    
    # Cleanup
    if 'TESTING' in os.environ:
        del os.environ['TESTING']


@pytest.fixture(scope="function")
def reset_singletons():
    """Reset all singletons before each test."""
    # Reset memory client singleton
    reset_memory_client()
    
    # Reset config manager singleton if it exists
    if hasattr(ConfigManager, '_instance'):
        ConfigManager._instance = None
    
    yield
    
    # Clean up after test
    reset_memory_client()


@pytest.fixture(scope="function")
def test_db():
    """Provide a clean test database for each test."""
    # Create temporary database file
    db_fd, db_path = tempfile.mkstemp(suffix='.db')
    os.close(db_fd)
    
    try:
        # Create test engine
        test_engine = create_engine(f'sqlite:///{db_path}', echo=False)
        
        # Create all tables
        Base.metadata.create_all(bind=test_engine)
        
        # Create session
        TestSession = sessionmaker(bind=test_engine)
        session = TestSession()
        
        yield session
        
        # Cleanup
        session.close()
        
    finally:
        # Remove temporary database file
        if os.path.exists(db_path):
            os.unlink(db_path)


@pytest.fixture(scope="function")
def test_user_and_app(test_db):
    """Create test user and app."""
    user, app = create_test_user_and_app(test_db)
    return user, app


@pytest.fixture(scope="function")
def mock_vector_store():
    """Provide a mock vector store for testing."""
    return MockVectorStore()


@pytest.fixture(scope="function")
def failing_vector_store():
    """Provide a mock vector store that fails operations."""
    return MockVectorStore(should_fail=True, failure_type="connection")


@pytest.fixture(scope="function")
def timeout_vector_store():
    """Provide a mock vector store that times out."""
    return MockVectorStore(should_fail=True, failure_type="timeout")


@pytest.fixture(scope="function")
def test_config():
    """Provide a test configuration."""
    return TestConfigManager.create_test_config()


@pytest.fixture(scope="function")
def test_config_no_vector():
    """Provide a test configuration without vector store."""
    return TestConfigManager.create_test_config(include_critical=False)


@pytest.fixture(scope="function")
def non_critical_config_change():
    """Provide a non-critical configuration change."""
    return TestConfigManager.create_non_critical_config_change()


@pytest.fixture(scope="function")
def critical_config_change():
    """Provide a critical configuration change."""
    return TestConfigManager.create_critical_config_change()


@pytest.fixture(scope="function")
def concurrency_helper():
    """Provide a concurrency test helper."""
    return ConcurrencyTestHelper()


@pytest.fixture(scope="function")
def memory_singleton(reset_singletons):
    """Provide a fresh memory client singleton."""
    return MemoryClientSingleton()


@pytest.fixture(scope="function")
def config_manager():
    """Provide a config manager instance."""
    # Create a temporary config file
    config_fd, config_path = tempfile.mkstemp(suffix='.json')
    os.close(config_fd)
    
    try:
        # Initialize config manager with test config
        with patch('app.utils.config_manager.SessionLocal') as mock_session:
            mock_db = mock_session.return_value.__enter__.return_value
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            manager = ConfigManager()
            yield manager
            
    finally:
        # Cleanup
        if os.path.exists(config_path):
            os.unlink(config_path)


@pytest.fixture(scope="function")
def patched_memory_client(mock_vector_store):
    """Provide a patched memory client with mock vector store."""
    with patch('app.utils.memory.Memory') as mock_memory_class:
        mock_client = mock_memory_class.from_config.return_value
        mock_client.add = mock_vector_store.add
        mock_client.get = mock_vector_store.get
        mock_client.search = mock_vector_store.search
        mock_client.get_all = lambda user_id: {"results": list(mock_vector_store.stored_memories.values())}
        
        yield mock_client, mock_vector_store


@pytest.fixture(scope="function")
def patched_failing_memory_client(failing_vector_store):
    """Provide a patched memory client with failing vector store."""
    with patch('app.utils.memory.Memory') as mock_memory_class:
        mock_client = mock_memory_class.from_config.return_value
        mock_client.add = failing_vector_store.add
        mock_client.get = failing_vector_store.get
        mock_client.search = failing_vector_store.search
        mock_client.get_all = lambda user_id: failing_vector_store.search("", user_id)
        
        yield mock_client, failing_vector_store


@pytest.fixture(scope="function")
def test_memory_data():
    """Provide test memory data."""
    return [
        {
            "content": "Test memory 1",
            "metadata": {"test": True, "index": 1}
        },
        {
            "content": "Test memory 2", 
            "metadata": {"test": True, "index": 2}
        },
        {
            "content": "Test memory 3",
            "metadata": {"test": True, "index": 3}
        }
    ]


@pytest.fixture(scope="function")
def large_memory_content():
    """Provide large memory content for chunking tests."""
    return "This is a very long memory content. " * 1000  # ~34KB of text


# Pytest configuration
def pytest_configure(config):
    """Configure pytest."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "concurrent: mark test as concurrency test"
    )
    config.addinivalue_line(
        "markers", "reliability: mark test as reliability test"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names."""
    for item in items:
        # Add integration marker to integration tests
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Add slow marker to performance tests
        if "performance" in item.nodeid or "concurrent" in item.nodeid:
            item.add_marker(pytest.mark.slow)
        
        # Add concurrent marker to concurrency tests
        if "concurrent" in item.nodeid or "thread" in item.nodeid:
            item.add_marker(pytest.mark.concurrent)
        
        # Add reliability marker to reliability tests
        if "reliability" in item.nodeid or "degradation" in item.nodeid or "failure" in item.nodeid:
            item.add_marker(pytest.mark.reliability)


# Test timeout configuration
@pytest.fixture(autouse=True)
def test_timeout():
    """Set a reasonable timeout for all tests."""
    import signal
    
    def timeout_handler(signum, frame):
        pytest.fail("Test timed out after 60 seconds")
    
    # Set timeout for 60 seconds
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(60)
    
    yield
    
    # Cancel the alarm
    signal.alarm(0)


# Skip tests if dependencies are not available
def pytest_runtest_setup(item):
    """Skip tests if required dependencies are not available."""
    # Skip tests that require actual database if not available
    if "requires_db" in item.keywords:
        try:
            from app.database import engine
            engine.connect()
        except Exception:
            pytest.skip("Database not available")
    
    # Skip tests that require vector store if not available
    if "requires_vector_store" in item.keywords:
        try:
            # This would be a real check for vector store availability
            pass
        except Exception:
            pytest.skip("Vector store not available")
