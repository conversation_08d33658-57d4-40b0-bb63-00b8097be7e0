import threading
import time
import pytest
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.utils.memory import MemoryClient<PERSON>ingleton, get_memory_client, reset_memory_client


class TestConfigurationDecoupling:
    """Test suite for configuration decoupling functionality."""
    
    def setup_method(self):
        """Reset the singleton before each test."""
        reset_memory_client()
    
    def test_critical_config_hash_generation(self):
        """Test that critical config hash only includes critical parameters."""
        singleton = MemoryClientSingleton()
        
        # Base config with critical parameters
        base_config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "api_key": "test-key",
                    "temperature": 0.1,
                    "max_tokens": 1000
                }
            },
            "embedder": {
                "provider": "openai",
                "config": {
                    "model": "text-embedding-3-small",
                    "api_key": "test-key"
                }
            },
            "custom_fact_extraction_prompt": "Extract facts from this text"
        }
        
        # Config with only non-critical changes
        non_critical_config = base_config.copy()
        non_critical_config["llm"]["config"]["temperature"] = 0.5  # Non-critical
        non_critical_config["llm"]["config"]["max_tokens"] = 2000  # Non-critical
        non_critical_config["custom_fact_extraction_prompt"] = "Different prompt"  # Non-critical
        
        # Critical config hashes should be the same
        hash1 = singleton._get_critical_config_hash(base_config)
        hash2 = singleton._get_critical_config_hash(non_critical_config)
        
        assert hash1 == hash2, "Non-critical config changes should not affect critical hash"
    
    def test_critical_config_changes_trigger_reinit(self):
        """Test that critical config changes trigger reinitialization."""
        singleton = MemoryClientSingleton()
        
        # Base config
        base_config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "api_key": "test-key"
                }
            }
        }
        
        # Config with critical change (different model)
        critical_config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o",  # Critical change
                    "api_key": "test-key"
                }
            }
        }
        
        # Different critical config hashes
        hash1 = singleton._get_critical_config_hash(base_config)
        hash2 = singleton._get_critical_config_hash(critical_config)
        
        assert hash1 != hash2, "Critical config changes should affect critical hash"
    
    def test_non_critical_changes_preserve_client(self):
        """Test that non-critical config changes don't reinitialize the client."""
        singleton = MemoryClientSingleton()
        
        # Base config
        base_config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "api_key": "test-key",
                    "temperature": 0.1
                }
            }
        }
        
        # Get initial client
        client1 = singleton.get_client(base_config)
        initial_hash = singleton._config_hash
        
        # Config with non-critical changes
        non_critical_config = base_config.copy()
        non_critical_config["llm"]["config"]["temperature"] = 0.5  # Non-critical
        non_critical_config["custom_fact_extraction_prompt"] = "New prompt"  # Non-critical
        
        # Get client with non-critical changes
        client2 = singleton.get_client(non_critical_config)
        
        # Should be the same client instance and same config hash
        if client1 is not None and client2 is not None:
            assert client1 is client2, "Non-critical changes should preserve client instance"
        
        assert singleton._config_hash == initial_hash, "Config hash should not change for non-critical changes"
    
    def test_api_key_change_triggers_reinit(self):
        """Test that API key changes trigger reinitialization."""
        singleton = MemoryClientSingleton()
        
        # Base config
        base_config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "api_key": "test-key-1"
                }
            }
        }
        
        # Config with different API key
        new_key_config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "api_key": "test-key-2"  # Critical change
                }
            }
        }
        
        # Different critical config hashes
        hash1 = singleton._get_critical_config_hash(base_config)
        hash2 = singleton._get_critical_config_hash(new_key_config)
        
        assert hash1 != hash2, "API key changes should affect critical hash"
    
    def test_provider_change_triggers_reinit(self):
        """Test that provider changes trigger reinitialization."""
        singleton = MemoryClientSingleton()
        
        # OpenAI config
        openai_config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "api_key": "test-key"
                }
            }
        }
        
        # Ollama config
        ollama_config = {
            "llm": {
                "provider": "ollama",  # Critical change
                "config": {
                    "model": "llama2",
                    "base_url": "http://localhost:11434"
                }
            }
        }
        
        # Different critical config hashes
        hash1 = singleton._get_critical_config_hash(openai_config)
        hash2 = singleton._get_critical_config_hash(ollama_config)
        
        assert hash1 != hash2, "Provider changes should affect critical hash"
    
    def test_vector_store_change_triggers_reinit(self):
        """Test that vector store changes trigger reinitialization."""
        singleton = MemoryClientSingleton()
        
        # Config with vector store 1
        config1 = {
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "host": "localhost",
                    "port": 6333
                }
            }
        }
        
        # Config with vector store 2
        config2 = {
            "vector_store": {
                "provider": "chroma",  # Critical change
                "config": {
                    "path": "/tmp/chroma"
                }
            }
        }
        
        # Different critical config hashes
        hash1 = singleton._get_critical_config_hash(config1)
        hash2 = singleton._get_critical_config_hash(config2)
        
        assert hash1 != hash2, "Vector store changes should affect critical hash"
    
    def test_should_reinitialize_logic(self):
        """Test the _should_reinitialize method logic."""
        singleton = MemoryClientSingleton()
        
        # Test with no client - should reinitialize
        config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "api_key": "test-key"
                }
            }
        }
        
        assert singleton._should_reinitialize(config), "Should reinitialize when no client exists"
        
        # Mock a client with proper attributes and config hash
        class MockClient:
            def __init__(self):
                self.config = {"test": "config"}
        
        singleton._client = MockClient()
        singleton._config_hash = singleton._get_critical_config_hash(config)
        singleton._last_health_check = time.time()  # Set recent health check
        
        # Same config should not reinitialize
        assert not singleton._should_reinitialize(config), "Should not reinitialize for same config"
        
        # Different critical config should reinitialize
        different_config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o",  # Different model
                    "api_key": "test-key"
                }
            }
        }
        
        assert singleton._should_reinitialize(different_config), "Should reinitialize for different critical config"
    
    def test_concurrent_config_changes(self):
        """Test concurrent access with config changes."""
        singleton = MemoryClientSingleton()
        results = []
        errors = []
        
        def access_with_config(config_variant):
            try:
                config = {
                    "llm": {
                        "provider": "openai",
                        "config": {
                            "model": "gpt-4o-mini",
                            "api_key": "test-key",
                            "temperature": config_variant  # Non-critical change
                        }
                    }
                }
                client = singleton.get_client(config)
                results.append((singleton, client, config_variant))
            except Exception as e:
                errors.append(e)
        
        # Create threads with different non-critical config variants
        threads = []
        for i in range(10):
            thread = threading.Thread(target=access_with_config, args=(0.1 + i * 0.01,))
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # Check that no errors occurred
        assert len(errors) == 0, f"Errors occurred: {errors}"
        
        # All singletons should be the same instance
        if results:
            first_singleton = results[0][0]
            for singleton_instance, client, variant in results:
                assert singleton_instance is first_singleton


if __name__ == "__main__":
    pytest.main([__file__, "-v"])