"""
Reliability and failure scenario tests for the memory system.

This module tests the system's ability to handle failures gracefully:
- Graceful degradation when vector store is unavailable
- Recovery from degraded mode
- Retry logic validation
- Backlog processing after recovery
- Error handling and logging verification
"""

import pytest
import time
import threading
from unittest.mock import patch, Mock, call
import logging

from app.utils.memory import Memory<PERSON><PERSON><PERSON><PERSON><PERSON>, reset_memory_client
from app.enhanced_logging import MemoryOperationStatus
from tests.utils.test_helpers import (
    MockVectorStore, wait_for_condition, assert_memory_operation_result
)


class TestGracefulDegradation:
    """Test graceful degradation scenarios."""
    
    def test_vector_store_unavailable_degradation(self, memory_singleton, test_config):
        """Test graceful degradation when vector store is unavailable."""
        # Mock failing vector store
        with patch('app.utils.memory.Memory') as mock_memory_class:
            failing_client = Mock()
            failing_client.add.side_effect = ConnectionError("Vector store unavailable")
            mock_memory_class.from_config.return_value = failing_client
            
            # Mock database-only storage
            with patch.object(memory_singleton, '_store_in_database_only') as mock_db_store:
                mock_db_store.return_value = {
                    "id": "db-only-id", 
                    "status": "success", 
                    "fallback_mode": True
                }
                
                # Initialize client
                client = memory_singleton.get_client(test_config)
                
                # Try to add memory - should trigger degradation
                result = memory_singleton.add_memory_with_degradation(
                    "Degradation test content",
                    {"test": "degradation"}
                )
                
                # Verify degraded mode was used
                assert result is not None, "Operation should complete in degraded mode"
                assert result.get("fallback_mode", False), "Should indicate fallback mode"
                assert mock_db_store.called, "Database-only storage should be called"
                
                # Verify memory was added to backlog
                assert len(memory_singleton._operation_backlog) > 0, "Operation should be in backlog"
                backlog_item = memory_singleton._operation_backlog[0]
                assert backlog_item["type"] == "add_memory"
                assert backlog_item["content"] == "Degradation test content"
    
    def test_degraded_mode_flag_management(self, memory_singleton, test_config):
        """Test that degraded mode flag is properly managed."""
        with patch('app.utils.memory.Memory') as mock_memory_class:
            # Start with failing client
            failing_client = Mock()
            failing_client.add.side_effect = ConnectionError("Connection failed")
            mock_memory_class.from_config.return_value = failing_client
            
            # Initialize client
            client = memory_singleton.get_client(test_config)
            
            # Verify not in degraded mode initially
            assert not memory_singleton._degraded_mode, "Should not start in degraded mode"
            
            # Trigger degradation
            with patch.object(memory_singleton, '_store_in_database_only') as mock_db:
                mock_db.return_value = {"id": "test", "status": "success", "fallback_mode": True}
                
                result = memory_singleton.add_memory_with_degradation("test")
                
                # Should now be in degraded mode
                assert memory_singleton._degraded_mode, "Should be in degraded mode after failure"
    
    def test_backlog_size_limit(self, memory_singleton, test_config):
        """Test that backlog respects size limits."""
        with patch('app.utils.memory.Memory') as mock_memory_class:
            failing_client = Mock()
            failing_client.add.side_effect = ConnectionError("Connection failed")
            mock_memory_class.from_config.return_value = failing_client
            
            # Set small backlog size for testing
            memory_singleton._max_backlog_size = 3
            
            with patch.object(memory_singleton, '_store_in_database_only') as mock_db:
                mock_db.return_value = {"id": "test", "status": "success", "fallback_mode": True}
                
                # Initialize client
                client = memory_singleton.get_client(test_config)
                
                # Add more items than backlog size
                for i in range(5):
                    memory_singleton.add_memory_with_degradation(f"Backlog test {i}")
                
                # Backlog should not exceed max size
                assert len(memory_singleton._operation_backlog) <= 3, "Backlog should respect size limit"
                
                # Should contain the most recent items
                backlog_contents = [item["content"] for item in memory_singleton._operation_backlog]
                assert "Backlog test 4" in backlog_contents, "Should contain most recent item"


class TestRecoveryScenarios:
    """Test recovery from degraded mode."""
    
    def test_vector_store_recovery(self, memory_singleton, test_config):
        """Test recovery when vector store becomes available again."""
        with patch('app.utils.memory.Memory') as mock_memory_class:
            # Start with failing client
            failing_client = Mock()
            failing_client.add.side_effect = ConnectionError("Connection failed")
            
            # Create working client for recovery
            working_client = Mock()
            working_client.add.return_value = {"id": "recovered-id", "status": "success"}
            
            # First call fails, subsequent calls succeed
            mock_memory_class.from_config.side_effect = [failing_client, working_client, working_client]
            
            # Initialize with failing client
            client = memory_singleton.get_client(test_config)
            
            # Add items to backlog
            with patch.object(memory_singleton, '_store_in_database_only') as mock_db:
                mock_db.return_value = {"id": "db-id", "status": "success", "fallback_mode": True}
                
                for i in range(3):
                    memory_singleton.add_memory_with_degradation(f"Recovery test {i}")
                
                assert len(memory_singleton._operation_backlog) == 3, "Should have backlog items"
                assert memory_singleton._degraded_mode, "Should be in degraded mode"
            
            # Attempt recovery
            memory_singleton._attempt_vector_store_recovery()
            
            # Should exit degraded mode
            assert not memory_singleton._degraded_mode, "Should exit degraded mode after recovery"
            
            # Backlog should be processed
            assert len(memory_singleton._operation_backlog) == 0, "Backlog should be cleared after recovery"
    
    def test_partial_backlog_recovery(self, memory_singleton, test_config):
        """Test handling of partial backlog recovery failures."""
        with patch('app.utils.memory.Memory') as mock_memory_class:
            # Create client that fails on some operations
            partial_client = Mock()
            
            def partial_add(content, metadata=None):
                if "fail" in content:
                    raise ConnectionError("Partial failure")
                return {"id": "success-id", "status": "success"}
            
            partial_client.add.side_effect = partial_add
            mock_memory_class.from_config.return_value = partial_client
            
            # Initialize client and add to backlog
            client = memory_singleton.get_client(test_config)
            
            with patch.object(memory_singleton, '_store_in_database_only') as mock_db:
                mock_db.return_value = {"id": "db-id", "status": "success", "fallback_mode": True}
                
                # Add mix of items that will succeed and fail during recovery
                memory_singleton.add_memory_with_degradation("Success item 1")
                memory_singleton.add_memory_with_degradation("Fail item")
                memory_singleton.add_memory_with_degradation("Success item 2")
                
                assert len(memory_singleton._operation_backlog) == 3
            
            # Attempt recovery
            memory_singleton._attempt_vector_store_recovery()
            
            # Should have processed successful items and kept failed ones
            remaining_items = [item["content"] for item in memory_singleton._operation_backlog]
            assert "Fail item" in remaining_items, "Failed items should remain in backlog"
            assert len(memory_singleton._operation_backlog) == 1, "Only failed items should remain"


class TestRetryLogic:
    """Test retry logic for failed operations."""
    
    def test_retry_on_transient_failure(self, memory_singleton, test_config):
        """Test retry logic for transient failures."""
        with patch('app.utils.memory.Memory') as mock_memory_class:
            # Create client that fails first few times then succeeds
            retry_client = Mock()
            call_count = 0
            
            def retry_add(content, metadata=None):
                nonlocal call_count
                call_count += 1
                if call_count < 3:  # Fail first 2 attempts
                    raise ConnectionError("Transient failure")
                return {"id": "retry-success", "status": "success"}
            
            retry_client.add.side_effect = retry_add
            mock_memory_class.from_config.return_value = retry_client
            
            # Initialize client
            client = memory_singleton.get_client(test_config)
            
            # Mock the retry decorator behavior
            with patch('app.mcp_server.add_memory_with_retry') as mock_retry:
                mock_retry.side_effect = retry_add
                
                # This should succeed after retries
                result = mock_retry("Retry test content")
                
                assert result["status"] == "success", "Should succeed after retries"
                assert call_count >= 3, "Should have retried multiple times"
    
    def test_retry_exhaustion(self, memory_singleton, test_config):
        """Test behavior when retry attempts are exhausted."""
        with patch('app.utils.memory.Memory') as mock_memory_class:
            # Create client that always fails
            failing_client = Mock()
            failing_client.add.side_effect = ConnectionError("Persistent failure")
            mock_memory_class.from_config.return_value = failing_client
            
            # Initialize client
            client = memory_singleton.get_client(test_config)
            
            # Mock database fallback for when retries are exhausted
            with patch.object(memory_singleton, '_store_in_database_only') as mock_db:
                mock_db.return_value = {"id": "fallback-id", "status": "success", "fallback_mode": True}
                
                # This should eventually fall back to degraded mode
                result = memory_singleton.add_memory_with_degradation("Exhausted retry test")
                
                assert result.get("fallback_mode", False), "Should fall back after retry exhaustion"
                assert memory_singleton._degraded_mode, "Should enter degraded mode"


class TestErrorHandlingAndLogging:
    """Test comprehensive error handling and logging."""
    
    def test_error_classification(self):
        """Test that errors are properly classified."""
        from app.enhanced_logging import classify_error
        
        # Test different error types
        connection_error = ConnectionError("Connection refused")
        timeout_error = TimeoutError("Operation timed out")
        auth_error = Exception("Authentication failed")
        validation_error = ValueError("Invalid input")
        
        assert classify_error(connection_error) == MemoryOperationStatus.CONNECTION_ERROR
        assert classify_error(timeout_error) == MemoryOperationStatus.TIMEOUT
        assert classify_error(auth_error) == MemoryOperationStatus.AUTHENTICATION_ERROR
        assert classify_error(validation_error) == MemoryOperationStatus.VALIDATION_ERROR
    
    def test_operation_logging(self, memory_singleton, test_config, caplog):
        """Test that operations are properly logged."""
        with patch('app.utils.memory.Memory') as mock_memory_class:
            mock_client = Mock()
            mock_client.add.return_value = {"id": "logged-id", "status": "success"}
            mock_memory_class.from_config.return_value = mock_client
            
            # Initialize client
            client = memory_singleton.get_client(test_config)
            
            # Perform operation
            with caplog.at_level(logging.INFO):
                success, message = memory_singleton.add_memory_async("Logging test")
                
                # Wait for async operation
                memory_singleton.wait_for_queue_empty(timeout=5.0)
            
            # Verify logging occurred
            log_messages = [record.message for record in caplog.records]
            assert any("Memory operation worker thread" in msg for msg in log_messages), "Should log worker activity"
    
    def test_degradation_status_reporting(self, memory_singleton, test_config):
        """Test that degradation status is properly reported."""
        from app.degradation_status import get_system_health_status
        
        with patch('app.utils.memory.Memory') as mock_memory_class:
            failing_client = Mock()
            failing_client.add.side_effect = ConnectionError("Vector store down")
            mock_memory_class.from_config.return_value = failing_client
            
            # Initialize client
            client = memory_singleton.get_client(test_config)
            
            # Trigger degradation
            with patch.object(memory_singleton, '_store_in_database_only') as mock_db:
                mock_db.return_value = {"id": "test", "status": "success", "fallback_mode": True}
                
                memory_singleton.add_memory_with_degradation("Status test")
                
                # Check system health status
                health_status = get_system_health_status()
                
                # Should reflect degraded state
                assert not health_status.get("vector_store_healthy", True), "Should report vector store as unhealthy"


class TestConcurrentFailureScenarios:
    """Test failure handling under concurrent load."""
    
    def test_concurrent_degradation_handling(self, memory_singleton, test_config, concurrency_helper):
        """Test that degradation is handled correctly under concurrent load."""
        with patch('app.utils.memory.Memory') as mock_memory_class:
            failing_client = Mock()
            failing_client.add.side_effect = ConnectionError("Concurrent failure")
            mock_memory_class.from_config.return_value = failing_client
            
            # Initialize client
            client = memory_singleton.get_client(test_config)
            
            with patch.object(memory_singleton, '_store_in_database_only') as mock_db:
                mock_db.return_value = {"id": "concurrent-db", "status": "success", "fallback_mode": True}
                
                # Define concurrent operation
                def add_memory_concurrent(index):
                    return memory_singleton.add_memory_with_degradation(f"Concurrent failure test {index}")
                
                # Run concurrent operations
                args_list = [(i,) for i in range(20)]
                results = concurrency_helper.run_concurrent_operations(
                    add_memory_concurrent, args_list, max_workers=10
                )
                
                # All operations should complete (in degraded mode)
                assert results["error_count"] == 0, f"No errors should occur: {results['errors']}"
                assert results["success_count"] == 20, "All operations should complete in degraded mode"
                
                # Should have backlog items
                assert len(memory_singleton._operation_backlog) > 0, "Should have backlog items"
                assert memory_singleton._degraded_mode, "Should be in degraded mode"
