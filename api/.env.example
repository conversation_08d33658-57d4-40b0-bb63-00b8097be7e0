OPENAI_API_KEY=sk-xxx
USER=user

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_JWT_SECRET=your_supabase_jwt_secret
SUPABASE_DATABASE_URL=postgresql://postgres:[password]@[host]:[port]/postgres

# Authentication Feature Flag
AUTH_ENABLED=false

# Database Configuration - Now uses Supabase only
# SUPABASE_DATABASE_URL is required and should point to your Supabase PostgreSQL instance