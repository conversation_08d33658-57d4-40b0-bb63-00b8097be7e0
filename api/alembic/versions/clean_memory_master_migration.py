"""Clean migration for memory_master schema

Revision ID: clean_memory_master
Revises: add_config_table
Create Date: 2025-06-14 15:33:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'clean_memory_master'
down_revision: Union[str, None] = 'add_config_table'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Drop all existing schemas and recreate clean
    op.execute("DROP SCHEMA IF EXISTS public CASCADE")
    op.execute("CREATE SCHEMA public")
    op.execute("GRANT ALL ON SCHEMA public TO public")
    
    # Recreate alembic_version table and populate with current revision
    op.execute("CREATE TABLE alembic_version (version_num VARCHAR(32) NOT NULL, CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num))")
    op.execute("INSERT INTO alembic_version (version_num) VALUES ('add_config_table')")
    
    # Create the memory_master schema
    op.execute("CREATE SCHEMA IF NOT EXISTS memory_master")
    
    # Create all new tables in memory_master schema
    op.create_table('access_controls',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('subject_type', sa.String(), nullable=False),
    sa.Column('subject_id', sa.UUID(), nullable=True),
    sa.Column('object_type', sa.String(), nullable=False),
    sa.Column('object_id', sa.UUID(), nullable=True),
    sa.Column('effect', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    schema='memory_master'
    )
    op.create_index('idx_access_object', 'access_controls', ['object_type', 'object_id'], unique=False, schema='memory_master')
    op.create_index('idx_access_subject', 'access_controls', ['subject_type', 'subject_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_access_controls_created_at'), 'access_controls', ['created_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_access_controls_effect'), 'access_controls', ['effect'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_access_controls_object_id'), 'access_controls', ['object_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_access_controls_object_type'), 'access_controls', ['object_type'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_access_controls_subject_id'), 'access_controls', ['subject_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_access_controls_subject_type'), 'access_controls', ['subject_type'], unique=False, schema='memory_master')
    
    op.create_table('archive_policies',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('criteria_type', sa.String(), nullable=False),
    sa.Column('criteria_id', sa.UUID(), nullable=True),
    sa.Column('days_to_archive', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    schema='memory_master'
    )
    op.create_index('idx_policy_criteria', 'archive_policies', ['criteria_type', 'criteria_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_archive_policies_created_at'), 'archive_policies', ['created_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_archive_policies_criteria_id'), 'archive_policies', ['criteria_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_archive_policies_criteria_type'), 'archive_policies', ['criteria_type'], unique=False, schema='memory_master')
    
    op.create_table('categories',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    schema='memory_master'
    )
    op.create_index(op.f('ix_memory_master_categories_created_at'), 'categories', ['created_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_categories_name'), 'categories', ['name'], unique=True, schema='memory_master')
    
    op.create_table('configs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('key', sa.String(), nullable=False),
    sa.Column('value', sa.JSON(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    schema='memory_master'
    )
    op.create_index(op.f('ix_memory_master_configs_key'), 'configs', ['key'], unique=True, schema='memory_master')
    
    op.create_table('users',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('supabase_user_id', sa.UUID(), nullable=True),
    sa.Column('email_verified', sa.Boolean(), nullable=True),
    sa.Column('last_sign_in_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('supabase_user_id'),
    schema='memory_master'
    )
    op.create_index(op.f('ix_memory_master_users_created_at'), 'users', ['created_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_users_email'), 'users', ['email'], unique=True, schema='memory_master')
    op.create_index(op.f('ix_memory_master_users_name'), 'users', ['name'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_users_user_id'), 'users', ['user_id'], unique=True, schema='memory_master')
    
    op.create_table('apps',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('owner_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('metadata', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['memory_master.users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='memory_master'
    )
    op.create_index(op.f('ix_memory_master_apps_created_at'), 'apps', ['created_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_apps_is_active'), 'apps', ['is_active'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_apps_name'), 'apps', ['name'], unique=True, schema='memory_master')
    op.create_index(op.f('ix_memory_master_apps_owner_id'), 'apps', ['owner_id'], unique=False, schema='memory_master')
    
    op.create_table('memories',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('app_id', sa.UUID(), nullable=False),
    sa.Column('content', sa.String(), nullable=False),
    sa.Column('vector', sa.String(), nullable=True),
    sa.Column('metadata', sa.JSON(), nullable=True),
    sa.Column('state', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('archived_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['app_id'], ['memory_master.apps.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['memory_master.users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='memory_master'
    )
    op.create_index('idx_memory_app_state', 'memories', ['app_id', 'state'], unique=False, schema='memory_master')
    op.create_index('idx_memory_user_app', 'memories', ['user_id', 'app_id'], unique=False, schema='memory_master')
    op.create_index('idx_memory_user_state', 'memories', ['user_id', 'state'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memories_app_id'), 'memories', ['app_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memories_archived_at'), 'memories', ['archived_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memories_created_at'), 'memories', ['created_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memories_deleted_at'), 'memories', ['deleted_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memories_state'), 'memories', ['state'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memories_user_id'), 'memories', ['user_id'], unique=False, schema='memory_master')
    
    op.create_table('memory_access_logs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('memory_id', sa.UUID(), nullable=False),
    sa.Column('app_id', sa.UUID(), nullable=False),
    sa.Column('accessed_at', sa.DateTime(), nullable=True),
    sa.Column('access_type', sa.String(), nullable=False),
    sa.Column('metadata', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['app_id'], ['memory_master.apps.id'], ),
    sa.ForeignKeyConstraint(['memory_id'], ['memory_master.memories.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='memory_master'
    )
    op.create_index('idx_access_app_time', 'memory_access_logs', ['app_id', 'accessed_at'], unique=False, schema='memory_master')
    op.create_index('idx_access_memory_time', 'memory_access_logs', ['memory_id', 'accessed_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memory_access_logs_access_type'), 'memory_access_logs', ['access_type'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memory_access_logs_accessed_at'), 'memory_access_logs', ['accessed_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memory_access_logs_app_id'), 'memory_access_logs', ['app_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memory_access_logs_memory_id'), 'memory_access_logs', ['memory_id'], unique=False, schema='memory_master')
    
    op.create_table('memory_categories',
    sa.Column('memory_id', sa.UUID(), nullable=False),
    sa.Column('category_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['memory_master.categories.id'], ),
    sa.ForeignKeyConstraint(['memory_id'], ['memory_master.memories.id'], ),
    sa.PrimaryKeyConstraint('memory_id', 'category_id'),
    schema='memory_master'
    )
    op.create_index('idx_memory_category', 'memory_categories', ['memory_id', 'category_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memory_categories_category_id'), 'memory_categories', ['category_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memory_categories_memory_id'), 'memory_categories', ['memory_id'], unique=False, schema='memory_master')
    
    op.create_table('memory_status_history',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('memory_id', sa.UUID(), nullable=False),
    sa.Column('changed_by', sa.UUID(), nullable=False),
    sa.Column('old_state', sa.String(), nullable=False),
    sa.Column('new_state', sa.String(), nullable=False),
    sa.Column('changed_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by'], ['memory_master.users.id'], ),
    sa.ForeignKeyConstraint(['memory_id'], ['memory_master.memories.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='memory_master'
    )
    op.create_index('idx_history_memory_state', 'memory_status_history', ['memory_id', 'new_state'], unique=False, schema='memory_master')
    op.create_index('idx_history_user_time', 'memory_status_history', ['changed_by', 'changed_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memory_status_history_changed_at'), 'memory_status_history', ['changed_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memory_status_history_changed_by'), 'memory_status_history', ['changed_by'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memory_status_history_memory_id'), 'memory_status_history', ['memory_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memory_status_history_new_state'), 'memory_status_history', ['new_state'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_memory_status_history_old_state'), 'memory_status_history', ['old_state'], unique=False, schema='memory_master')


def downgrade() -> None:
    """Downgrade schema."""
    # Drop the memory_master schema and all its tables
    op.execute("DROP SCHEMA IF EXISTS memory_master CASCADE")