"""
Database Service Layer
Provides high-level database operations for Supabase PostgreSQL
"""
import logging
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, text

from app.models import (
    User, App, Config, Memory, Category, AccessControl,
    ArchivePolicy, MemoryStatusHistory, MemoryAccessLog, memory_categories
)
from app.database import get_db

logger = logging.getLogger(__name__)


class DatabaseService:
    """High-level database service for Supabase PostgreSQL"""
    
    def __init__(self):
        pass
    
    # User operations
    def create_user(self, user_id: str, name: Optional[str] = None, 
                   email: Optional[str] = None, metadata: Optional[Dict] = None,
                   supabase_user_id: Optional[UUID] = None) -> User:
        """Create a new user"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            user = User(
                user_id=user_id,
                name=name,
                email=email,
                metadata_=metadata or {},
                supabase_user_id=supabase_user_id
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            return user
        finally:
            db.close()
    
    def get_user(self, user_id: Optional[str] = None, id: Optional[UUID] = None) -> Optional[User]:
        """Get user by user_id or id"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            query = db.query(User)
            if user_id:
                query = query.filter(User.user_id == user_id)
            elif id:
                query = query.filter(User.id == id)
            else:
                return None
            return query.first()
        finally:
            db.close()
    
    def update_user(self, user_id: UUID, **kwargs) -> User:
        """Update user"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                raise ValueError(f"User not found: {user_id}")
            
            for key, value in kwargs.items():
                if hasattr(user, key):
                    setattr(user, key, value)
            
            if hasattr(user, 'updated_at'):
                user.updated_at = datetime.now(timezone.utc)
            
            db.commit()
            db.refresh(user)
            return user
        finally:
            db.close()
    
    # App operations
    def create_app(self, owner_id: UUID, name: str, description: Optional[str] = None,
                   metadata: Optional[Dict] = None) -> App:
        """Create a new app"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            app = App(
                owner_id=owner_id,
                name=name,
                description=description,
                metadata_=metadata or {}
            )
            db.add(app)
            db.commit()
            db.refresh(app)
            return app
        finally:
            db.close()
    
    def get_app(self, app_id: Optional[UUID] = None, name: Optional[str] = None) -> Optional[App]:
        """Get app by id or name"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            query = db.query(App)
            if app_id:
                query = query.filter(App.id == app_id)
            elif name:
                query = query.filter(App.name == name)
            else:
                return None
            return query.first()
        finally:
            db.close()
    
    def get_apps_for_user(self, user_id: UUID, is_active: bool = True) -> List[App]:
        """Get all apps for a user"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            query = db.query(App).filter(App.owner_id == user_id)
            if is_active is not None:
                query = query.filter(App.is_active == is_active)
            return query.all()
        finally:
            db.close()
    
    def update_app(self, app_id: UUID, **kwargs) -> App:
        """Update app"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            app = db.query(App).filter(App.id == app_id).first()
            if not app:
                raise ValueError(f"App not found: {app_id}")
            
            for key, value in kwargs.items():
                if hasattr(app, key):
                    setattr(app, key, value)
            
            if hasattr(app, 'updated_at'):
                app.updated_at = datetime.now(timezone.utc)
            
            db.commit()
            db.refresh(app)
            return app
        finally:
            db.close()
    
    # Memory operations
    def create_memory(self, user_id: UUID, app_id: UUID, content: str,
                     vector: Optional[str] = None, metadata: Optional[Dict] = None,
                     state: str = "active") -> Memory:
        """Create a new memory"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            memory = Memory(
                user_id=user_id,
                app_id=app_id,
                content=content,
                vector=vector,
                metadata_=metadata or {},
                state=state
            )
            db.add(memory)
            db.commit()
            db.refresh(memory)
            return memory
        finally:
            db.close()
    
    def get_memory(self, memory_id: UUID) -> Optional[Memory]:
        """Get memory by id"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            return db.query(Memory).filter(Memory.id == memory_id).first()
        finally:
            db.close()
    
    def get_memories(self, user_id: Optional[UUID] = None, app_id: Optional[UUID] = None,
                    state: Optional[str] = None, limit: int = 100, offset: int = 0,
                    order_by: str = "created_at", order_desc: bool = True) -> List[Memory]:
        """Get memories with filtering and pagination"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            query = db.query(Memory)
            
            if user_id:
                query = query.filter(Memory.user_id == user_id)
            if app_id:
                query = query.filter(Memory.app_id == app_id)
            if state:
                query = query.filter(Memory.state == state)
            
            # Order by
            if hasattr(Memory, order_by):
                column = getattr(Memory, order_by)
                if order_desc:
                    query = query.order_by(desc(column))
                else:
                    query = query.order_by(asc(column))
            
            return query.offset(offset).limit(limit).all()
        finally:
            db.close()
    
    def update_memory(self, memory_id: UUID, **kwargs) -> Memory:
        """Update memory"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            memory = db.query(Memory).filter(Memory.id == memory_id).first()
            if not memory:
                raise ValueError(f"Memory not found: {memory_id}")
            
            for key, value in kwargs.items():
                if hasattr(memory, key):
                    setattr(memory, key, value)
            
            if hasattr(memory, 'updated_at'):
                memory.updated_at = datetime.now(timezone.utc)
            
            db.commit()
            db.refresh(memory)
            return memory
        finally:
            db.close()
    
    def delete_memory(self, memory_id: UUID, soft_delete: bool = True) -> Memory:
        """Delete or archive memory"""
        if soft_delete:
            # Use update for soft delete
            return self.update_memory(
                memory_id,
                state="deleted",
                deleted_at=datetime.now(timezone.utc)
            )
        else:
            # Hard delete
            db_gen = get_db()
            db = next(db_gen)
            try:
                memory = db.query(Memory).filter(Memory.id == memory_id).first()
                if not memory:
                    raise ValueError(f"Memory not found: {memory_id}")
                
                db.delete(memory)
                db.commit()
                return memory
            finally:
                db.close()
    
    def search_memories(self, user_id: UUID, query: str, app_id: Optional[UUID] = None,
                       limit: int = 50) -> List[Memory]:
        """Search memories by content"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            db_query = db.query(Memory).filter(
                and_(
                    Memory.user_id == user_id,
                    Memory.content.contains(query),
                    Memory.state == "active"
                )
            )
            
            if app_id:
                db_query = db_query.filter(Memory.app_id == app_id)
            
            return db_query.limit(limit).all()
        finally:
            db.close()
    
    # Category operations
    def create_category(self, name: str, description: Optional[str] = None) -> Category:
        """Create a new category"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            category = Category(
                name=name,
                description=description
            )
            db.add(category)
            db.commit()
            db.refresh(category)
            return category
        finally:
            db.close()
    
    def get_category(self, category_id: Optional[UUID] = None, name: Optional[str] = None) -> Optional[Category]:
        """Get category by id or name"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            query = db.query(Category)
            if category_id:
                query = query.filter(Category.id == category_id)
            elif name:
                query = query.filter(Category.name == name)
            else:
                return None
            return query.first()
        finally:
            db.close()
    
    def get_categories(self, limit: int = 100) -> List[Category]:
        """Get all categories"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            return db.query(Category).limit(limit).all()
        finally:
            db.close()
    
    def add_memory_category(self, memory_id: UUID, category_id: UUID):
        """Add category to memory"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            # Use raw SQL since this is a many-to-many relationship
            db.execute(
                text("""
                INSERT INTO memory_master.memory_categories (memory_id, category_id)
                VALUES (:memory_id, :category_id)
                ON CONFLICT DO NOTHING
                """),
                {"memory_id": str(memory_id), "category_id": str(category_id)}
            )
            db.commit()
        finally:
            db.close()
    
    def get_memory_categories(self, memory_id: UUID) -> List[Category]:
        """Get all categories for a memory"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            return db.query(Category).join(
                memory_categories
            ).filter(
                memory_categories.c.memory_id == memory_id
            ).all()
        finally:
            db.close()
    
    # Config operations
    def set_config(self, key: str, value: Any) -> Config:
        """Set configuration value"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            # Check if config exists
            config = db.query(Config).filter(Config.key == key).first()
            
            if config:
                # Update existing
                config.value = value
                if hasattr(config, 'updated_at'):
                    config.updated_at = datetime.now(timezone.utc)
            else:
                # Create new
                config = Config(key=key, value=value)
                db.add(config)
            
            db.commit()
            db.refresh(config)
            return config
        finally:
            db.close()
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            config = db.query(Config).filter(Config.key == key).first()
            return config.value if config else default
        finally:
            db.close()
    
    # Statistics and reporting
    def get_user_stats(self, user_id: UUID) -> Dict[str, Any]:
        """Get user statistics"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            stats = {
                "total_memories": db.query(Memory).filter(
                    and_(Memory.user_id == user_id, Memory.state == "active")
                ).count(),
                "total_apps": db.query(App).filter(
                    and_(App.owner_id == user_id, App.is_active == True)
                ).count(),
                "archived_memories": db.query(Memory).filter(
                    and_(Memory.user_id == user_id, Memory.state == "archived")
                ).count(),
                "deleted_memories": db.query(Memory).filter(
                    and_(Memory.user_id == user_id, Memory.state == "deleted")
                ).count()
            }
            
            # Get latest memory
            latest_memory = db.query(Memory).filter(
                and_(Memory.user_id == user_id, Memory.state == "active")
            ).order_by(desc(Memory.created_at)).first()
            
            if latest_memory:
                stats["latest_memory_date"] = latest_memory.created_at
                stats["latest_memory_app"] = latest_memory.app.name if latest_memory.app else None
            
            return stats
        finally:
            db.close()
    
    def get_app_stats(self, app_id: UUID) -> Dict[str, Any]:
        """Get app statistics"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            stats = {
                "total_memories": db.query(Memory).filter(
                    and_(Memory.app_id == app_id, Memory.state == "active")
                ).count(),
                "total_users": db.query(Memory.user_id).filter(
                    Memory.app_id == app_id
                ).distinct().count(),
                "archived_memories": db.query(Memory).filter(
                    and_(Memory.app_id == app_id, Memory.state == "archived")
                ).count()
            }
            
            # Get latest memory
            latest_memory = db.query(Memory).filter(
                and_(Memory.app_id == app_id, Memory.state == "active")
            ).order_by(desc(Memory.created_at)).first()
            
            if latest_memory:
                stats["latest_memory_date"] = latest_memory.created_at
                stats["latest_memory_user"] = latest_memory.user.name if latest_memory.user else None
            
            return stats
        finally:
            db.close()


# Create a singleton instance
db_service = DatabaseService()