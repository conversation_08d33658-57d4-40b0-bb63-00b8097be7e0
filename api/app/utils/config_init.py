"""
Configuration Initialization

This module handles the initialization of the configuration management system,
including setting up the ConfigManager and registering default listeners.
"""

import logging
import threading
import time
from typing import Optional

from app.utils.config_manager import get_config_manager
from app.utils.config_listeners import register_default_listeners


class ConfigInitializer:
    """
    Handles initialization and background monitoring of the configuration system.
    """
    
    def __init__(self):
        self._initialized = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._monitor_running = False
        self._monitor_interval = 10.0  # Check for config updates every 10 seconds
        
    def initialize(self) -> None:
        """
        Initialize the configuration management system.
        
        This should be called during application startup.
        """
        if self._initialized:
            logging.warning("ConfigInitializer already initialized")
            return
        
        try:
            logging.info("Initializing configuration management system...")
            
            # Get the ConfigManager instance (this will create it if it doesn't exist)
            config_manager = get_config_manager()
            
            # Register default configuration change listeners
            register_default_listeners(config_manager)
            
            # Start background monitoring for configuration changes
            self._start_background_monitor()
            
            self._initialized = True
            logging.info("Configuration management system initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize configuration management system: {e}")
            raise
    
    def _start_background_monitor(self) -> None:
        """Start background thread to monitor for configuration changes."""
        if self._monitor_thread and self._monitor_thread.is_alive():
            logging.warning("Configuration monitor thread already running")
            return
        
        self._monitor_running = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_config_changes,
            name="ConfigMonitor",
            daemon=True
        )
        self._monitor_thread.start()
        logging.info("Configuration monitor thread started")
    
    def _monitor_config_changes(self) -> None:
        """
        Background monitoring loop for configuration changes.
        
        This runs in a separate thread and periodically checks for
        configuration updates from the database.
        """
        config_manager = get_config_manager()
        
        while self._monitor_running:
            try:
                # Check for configuration updates
                updated = config_manager.check_for_updates()
                
                if updated:
                    logging.info("Configuration updated from external source")
                
            except Exception as e:
                logging.error(f"Error in configuration monitor: {e}")
            
            # Wait before next check
            time.sleep(self._monitor_interval)
        
        logging.info("Configuration monitor thread stopped")
    
    def shutdown(self) -> None:
        """
        Shutdown the configuration management system.
        
        This should be called during application shutdown.
        """
        if not self._initialized:
            return
        
        logging.info("Shutting down configuration management system...")
        
        # Stop background monitoring
        self._monitor_running = False
        
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5.0)
            if self._monitor_thread.is_alive():
                logging.warning("Configuration monitor thread did not stop gracefully")
        
        self._initialized = False
        logging.info("Configuration management system shutdown complete")
    
    def is_initialized(self) -> bool:
        """Check if the configuration system is initialized."""
        return self._initialized
    
    def get_status(self) -> dict:
        """Get status information about the configuration system."""
        return {
            "initialized": self._initialized,
            "monitor_running": self._monitor_running,
            "monitor_interval": self._monitor_interval,
            "monitor_thread_alive": self._monitor_thread.is_alive() if self._monitor_thread else False
        }


# Global initializer instance
_config_initializer = ConfigInitializer()


def initialize_config_system() -> None:
    """
    Initialize the configuration management system.
    
    This is the main entry point for setting up configuration hot-reload.
    Call this during application startup.
    """
    _config_initializer.initialize()


def shutdown_config_system() -> None:
    """
    Shutdown the configuration management system.
    
    Call this during application shutdown for clean cleanup.
    """
    _config_initializer.shutdown()


def get_config_system_status() -> dict:
    """Get status information about the configuration system."""
    return _config_initializer.get_status()


def is_config_system_initialized() -> bool:
    """Check if the configuration system is initialized."""
    return _config_initializer.is_initialized()


# Context manager for testing
class ConfigSystemContext:
    """Context manager for configuration system lifecycle in tests."""
    
    def __enter__(self):
        initialize_config_system()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        shutdown_config_system()


def create_config_test_context():
    """Create a context manager for testing configuration system."""
    return ConfigSystemContext()
