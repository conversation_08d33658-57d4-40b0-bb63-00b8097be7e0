"""
Configuration Hot-Reload Manager

This module provides a thread-safe singleton configuration manager that supports
hot-reloading of non-critical configuration changes without requiring application restart.
"""

import threading
import time
import logging
import hashlib
import json
from typing import Dict, Any, List, Callable, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from app.database import SessionLocal
from app.models import Config as ConfigModel


class ConfigChangeType(Enum):
    """Types of configuration changes."""
    NON_CRITICAL = "non_critical"  # Can be hot-reloaded
    CRITICAL = "critical"          # Requires restart
    VALIDATION_ERROR = "validation_error"  # Invalid configuration


@dataclass
class ConfigValidationResult:
    """Result of configuration validation."""
    valid: bool
    errors: List[str]
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


@dataclass
class ConfigChangeEvent:
    """Configuration change event data."""
    old_config: Dict[str, Any]
    new_config: Dict[str, Any]
    change_type: ConfigChangeType
    requires_restart: bool
    config_version: int
    timestamp: float


# Type alias for configuration change listeners
ConfigChangeListener = Callable[[ConfigChangeEvent], None]


class ConfigManager:
    """
    Thread-safe singleton configuration manager with hot-reload support.
    
    Features:
    - Hot-reload for non-critical configuration changes
    - Configuration validation with detailed error reporting
    - Configuration versioning and change tracking
    - Observer pattern for configuration change notifications
    - Thread-safe operations with proper locking
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __init__(self):
        """Initialize the configuration manager."""
        if ConfigManager._instance is not None:
            raise RuntimeError("ConfigManager is a singleton. Use get_instance() instead.")
        
        self._config: Dict[str, Any] = {}
        self._config_version: int = 0
        self._config_lock = threading.RLock()  # Reentrant lock for nested operations
        self._change_listeners: List[ConfigChangeListener] = []
        self._listeners_lock = threading.Lock()
        self._last_db_check: float = 0
        self._db_check_interval: float = 5.0  # Check database every 5 seconds
        self._config_hash: Optional[str] = None
        
        # Critical configuration fields that require restart
        self._critical_fields = {
            'mem0.llm.provider',
            'mem0.embedder.provider', 
            'mem0.llm.config.api_key',
            'mem0.embedder.config.api_key',
            'vector_store.provider',
            'vector_store.config'
        }
        
        # Load initial configuration
        self._load_config()
        
        logging.info("ConfigManager initialized successfully")
    
    @classmethod
    def get_instance(cls) -> 'ConfigManager':
        """Get the singleton instance of ConfigManager."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    def _load_config(self) -> None:
        """Load configuration from database."""
        try:
            db = SessionLocal()
            try:
                db_config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
                if db_config and db_config.value:
                    with self._config_lock:
                        self._config = db_config.value.copy()
                        self._config_version += 1
                        self._config_hash = self._calculate_config_hash(self._config)
                        self._last_db_check = time.time()
                        
                    logging.info(f"Configuration loaded from database (version {self._config_version})")
                else:
                    logging.warning("No configuration found in database, using empty config")
                    
            finally:
                db.close()
                
        except Exception as e:
            logging.error(f"Failed to load configuration from database: {e}")
            # Continue with empty config rather than failing
    
    def _calculate_config_hash(self, config: Dict[str, Any]) -> str:
        """Calculate hash of configuration for change detection."""
        try:
            config_str = json.dumps(config, sort_keys=True, default=str)
            return hashlib.sha256(config_str.encode()).hexdigest()
        except Exception as e:
            logging.warning(f"Failed to calculate config hash: {e}")
            return str(time.time())  # Fallback to timestamp
    
    def get_config(self) -> Dict[str, Any]:
        """Get current configuration (thread-safe copy)."""
        with self._config_lock:
            return self._config.copy()
    
    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """
        Get a specific configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to the config value (e.g., 'mem0.llm.config.model')
            default: Default value if key is not found
            
        Returns:
            Configuration value or default
        """
        with self._config_lock:
            try:
                value = self._config
                for key in key_path.split('.'):
                    value = value[key]
                return value
            except (KeyError, TypeError):
                return default
    
    def update_config(self, new_config: Dict[str, Any], save_to_db: bool = True) -> Dict[str, Any]:
        """
        Update configuration with validation and hot-reload support.
        
        Args:
            new_config: New configuration values to apply
            save_to_db: Whether to save changes to database
            
        Returns:
            Dictionary with update result information
            
        Raises:
            ValueError: If configuration validation fails
        """
        with self._config_lock:
            # Validate configuration changes
            validation_result = self._validate_config(new_config)
            if not validation_result.valid:
                raise ValueError(f"Invalid configuration: {', '.join(validation_result.errors)}")
            
            # Store old config for change event
            old_config = self._config.copy()
            
            # Determine change type and restart requirement
            change_type, requires_restart = self._analyze_config_changes(old_config, new_config)
            
            # Apply configuration changes
            self._config.update(new_config)
            self._config_version += 1
            new_hash = self._calculate_config_hash(self._config)
            
            # Save to database if requested
            if save_to_db:
                self._save_config_to_db()
            
            # Create change event
            change_event = ConfigChangeEvent(
                old_config=old_config,
                new_config=self._config.copy(),
                change_type=change_type,
                requires_restart=requires_restart,
                config_version=self._config_version,
                timestamp=time.time()
            )
            
            # Notify listeners
            self._notify_listeners(change_event)
            
            # Update hash after successful change
            self._config_hash = new_hash
            
            logging.info(f"Configuration updated to version {self._config_version} "
                        f"(type: {change_type.value}, restart_required: {requires_restart})")
            
            return {
                "success": True,
                "requires_restart": requires_restart,
                "config_version": self._config_version,
                "change_type": change_type.value,
                "warnings": validation_result.warnings
            }

    def _validate_config(self, config: Dict[str, Any]) -> ConfigValidationResult:
        """
        Validate configuration changes.

        Args:
            config: Configuration to validate

        Returns:
            ConfigValidationResult with validation details
        """
        errors = []
        warnings = []

        # Validate mem0 configuration if present
        if 'mem0' in config:
            mem0_config = config['mem0']

            # Validate LLM configuration
            if 'llm' in mem0_config:
                llm_config = mem0_config['llm']
                if 'provider' in llm_config and not llm_config['provider']:
                    errors.append("mem0.llm.provider cannot be empty")

                if 'config' in llm_config:
                    llm_inner_config = llm_config['config']

                    # Validate API key
                    if 'api_key' in llm_inner_config:
                        api_key = llm_inner_config['api_key']
                        if not api_key or (isinstance(api_key, str) and not api_key.strip()):
                            errors.append("mem0.llm.config.api_key cannot be empty")
                        elif isinstance(api_key, str) and api_key.startswith('env:'):
                            # Validate environment variable exists
                            env_var = api_key[4:]  # Remove 'env:' prefix
                            import os
                            if not os.getenv(env_var):
                                warnings.append(f"Environment variable {env_var} is not set")

                    # Validate model parameters
                    if 'temperature' in llm_inner_config:
                        temp = llm_inner_config['temperature']
                        if not isinstance(temp, (int, float)) or not (0 <= temp <= 1):
                            errors.append("mem0.llm.config.temperature must be a number between 0 and 1")

                    if 'max_tokens' in llm_inner_config:
                        max_tokens = llm_inner_config['max_tokens']
                        if not isinstance(max_tokens, int) or max_tokens <= 0:
                            errors.append("mem0.llm.config.max_tokens must be a positive integer")

            # Validate embedder configuration
            if 'embedder' in mem0_config:
                embedder_config = mem0_config['embedder']
                if 'provider' in embedder_config and not embedder_config['provider']:
                    errors.append("mem0.embedder.provider cannot be empty")

                if 'config' in embedder_config:
                    embedder_inner_config = embedder_config['config']

                    # Validate API key
                    if 'api_key' in embedder_inner_config:
                        api_key = embedder_inner_config['api_key']
                        if not api_key or (isinstance(api_key, str) and not api_key.strip()):
                            errors.append("mem0.embedder.config.api_key cannot be empty")

        # Validate openmemory configuration if present
        if 'openmemory' in config:
            openmemory_config = config['openmemory']

            if 'max_text_length' in openmemory_config:
                max_length = openmemory_config['max_text_length']
                if not isinstance(max_length, int) or max_length <= 0:
                    errors.append("openmemory.max_text_length must be a positive integer")
                elif max_length > 10000:
                    warnings.append("openmemory.max_text_length is very large, may impact performance")

        return ConfigValidationResult(
            valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )

    def _analyze_config_changes(self, old_config: Dict[str, Any], new_config: Dict[str, Any]) -> Tuple[ConfigChangeType, bool]:
        """
        Analyze configuration changes to determine if restart is required.

        Args:
            old_config: Previous configuration
            new_config: New configuration values

        Returns:
            Tuple of (change_type, requires_restart)
        """
        requires_restart = False

        # Check if any critical fields are being changed
        for field_path in self._critical_fields:
            old_value = self._get_nested_value(old_config, field_path)
            new_value = self._get_nested_value(new_config, field_path)

            if new_value is not None and old_value != new_value:
                requires_restart = True
                logging.info(f"Critical configuration change detected: {field_path}")
                break

        change_type = ConfigChangeType.CRITICAL if requires_restart else ConfigChangeType.NON_CRITICAL
        return change_type, requires_restart

    def _get_nested_value(self, config: Dict[str, Any], key_path: str) -> Any:
        """Get nested configuration value using dot notation."""
        try:
            value = config
            for key in key_path.split('.'):
                value = value[key]
            return value
        except (KeyError, TypeError):
            return None

    def _save_config_to_db(self) -> None:
        """Save current configuration to database."""
        try:
            db = SessionLocal()
            try:
                db_config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()

                if db_config:
                    db_config.value = self._config
                    db_config.updated_at = None  # Trigger automatic timestamp update
                else:
                    db_config = ConfigModel(key="main", value=self._config)
                    db.add(db_config)

                db.commit()
                logging.debug("Configuration saved to database")

            finally:
                db.close()

        except Exception as e:
            logging.error(f"Failed to save configuration to database: {e}")
            raise

    def add_change_listener(self, listener: ConfigChangeListener) -> None:
        """
        Add a configuration change listener.

        Args:
            listener: Callable that will be notified of configuration changes
        """
        with self._listeners_lock:
            if listener not in self._change_listeners:
                self._change_listeners.append(listener)
                logging.debug(f"Added configuration change listener: {listener.__name__}")

    def remove_change_listener(self, listener: ConfigChangeListener) -> None:
        """
        Remove a configuration change listener.

        Args:
            listener: Listener to remove
        """
        with self._listeners_lock:
            if listener in self._change_listeners:
                self._change_listeners.remove(listener)
                logging.debug(f"Removed configuration change listener: {listener.__name__}")

    def _notify_listeners(self, change_event: ConfigChangeEvent) -> None:
        """
        Notify all registered listeners of configuration changes.

        Args:
            change_event: Configuration change event data
        """
        with self._listeners_lock:
            listeners = self._change_listeners.copy()  # Copy to avoid lock during iteration

        for listener in listeners:
            try:
                listener(change_event)
            except Exception as e:
                logging.error(f"Error in configuration change listener {listener.__name__}: {e}")

    def check_for_updates(self) -> bool:
        """
        Check for configuration updates from database.

        Returns:
            True if configuration was updated, False otherwise
        """
        current_time = time.time()
        if current_time - self._last_db_check < self._db_check_interval:
            return False

        try:
            db = SessionLocal()
            try:
                db_config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
                if not db_config or not db_config.value:
                    return False

                # Calculate hash of database config
                db_config_hash = self._calculate_config_hash(db_config.value)

                with self._config_lock:
                    if db_config_hash != self._config_hash:
                        # Configuration has changed in database
                        old_config = self._config.copy()
                        self._config = db_config.value.copy()
                        self._config_version += 1
                        self._config_hash = db_config_hash

                        # Analyze changes and notify listeners
                        change_type, requires_restart = self._analyze_config_changes(old_config, self._config)

                        change_event = ConfigChangeEvent(
                            old_config=old_config,
                            new_config=self._config.copy(),
                            change_type=change_type,
                            requires_restart=requires_restart,
                            config_version=self._config_version,
                            timestamp=current_time
                        )

                        self._notify_listeners(change_event)

                        logging.info(f"Configuration updated from database (version {self._config_version})")
                        return True

                return False

            finally:
                db.close()
                self._last_db_check = current_time

        except Exception as e:
            logging.error(f"Failed to check for configuration updates: {e}")
            return False

    def get_version(self) -> int:
        """Get current configuration version."""
        with self._config_lock:
            return self._config_version

    def reset(self) -> None:
        """Reset configuration manager (mainly for testing)."""
        with self._config_lock:
            self._config = {}
            self._config_version = 0
            self._config_hash = None
            self._last_db_check = 0

        with self._listeners_lock:
            self._change_listeners.clear()

        logging.info("ConfigManager reset")


# Global instance accessor
def get_config_manager() -> ConfigManager:
    """Get the global ConfigManager instance."""
    return ConfigManager.get_instance()
