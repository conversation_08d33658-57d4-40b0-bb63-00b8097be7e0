import os
from typing import Optional
from supabase import create_client, Client
from jose import JW<PERSON>rror, jwt
from fastapi import HTT<PERSON>Ex<PERSON>, status
import logging

logger = logging.getLogger(__name__)

class SupabaseClient:
    def __init__(self):
        self.url = os.getenv("SUPABASE_URL")
        self.service_role_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") 
        self.jwt_secret = os.getenv("SUPABASE_JWT_SECRET")
        self.auth_enabled = os.getenv("AUTH_ENABLED", "false").lower() == "true"
        
        if self.auth_enabled and not all([self.url, self.service_role_key, self.jwt_secret]):
            logger.warning("Supabase configuration incomplete. Authentication will be disabled.")
            self.auth_enabled = False
        
        self.client: Optional[Client] = None
        if self.auth_enabled:
            try:
                self.client = create_client(self.url, self.service_role_key)
                logger.info("Supabase client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Supabase client: {e}")
                self.auth_enabled = False

    def verify_jwt_token(self, token: str) -> Optional[dict]:
        """
        Verify and decode a Supabase JWT token.
        Returns the decoded payload if valid, None otherwise.
        """
        if not self.auth_enabled or not self.jwt_secret:
            return None
            
        try:
            # Decode and verify the JWT token
            payload = jwt.decode(
                token,
                self.jwt_secret,
                algorithms=["HS256"],
                audience="authenticated"
            )
            return payload
        except JWTError as e:
            logger.warning(f"JWT verification failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during JWT verification: {e}")
            return None

    def get_user_by_id(self, user_id: str) -> Optional[dict]:
        """
        Get user information from Supabase by user ID.
        """
        if not self.auth_enabled or not self.client:
            return None
            
        try:
            response = self.client.auth.admin.get_user_by_id(user_id)
            return response.user.dict() if response.user else None
        except Exception as e:
            logger.error(f"Failed to get user by ID: {e}")
            return None

    def create_user_profile(self, user_data: dict) -> bool:
        """
        Create or update user profile in our internal database.
        """
        # This will be implemented when we integrate with the User model
        pass

# Global instance
supabase_client = SupabaseClient()