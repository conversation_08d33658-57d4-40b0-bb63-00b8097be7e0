"""Degradation status monitoring for OpenMemory.

This module provides tools to monitor and report on the graceful degradation
status of the memory system, including vector store connectivity and operation backlog.
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any

from app.utils.memory import Memory<PERSON>lient<PERSON><PERSON>leton


def get_system_health_status() -> Dict[str, Any]:
    """Get comprehensive system health status including degradation information."""
    try:
        # Get memory client instance
        memory_client = MemoryClientSingleton()
        
        # Get degradation status
        degradation_status = memory_client.get_degradation_status()
        
        # Get basic health information
        is_healthy = memory_client.is_healthy()
        health_status = memory_client.get_health_status()
        
        # Get operation queue status
        queue_status = memory_client.get_operation_queue_status()
        
        # Get operation metrics from enhanced logging
        operation_metrics = None
        try:
            from app.enhanced_logging import operation_logger
            operation_metrics = operation_logger.get_operation_metrics()
            active_operations = operation_logger.get_active_operations()
        except ImportError:
            logging.warning("Enhanced logging not available for operation metrics")
            operation_metrics = {"error": "Enhanced logging module not available"}
            active_operations = {}
        
        # Compile comprehensive status
        system_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_health": "healthy" if is_healthy and not degradation_status['degraded_mode'] else "degraded",
            "client_healthy": is_healthy,
            "degradation": degradation_status,
            "health_details": health_status,
            "operation_queue": queue_status,
            "operation_metrics": operation_metrics,
            "active_operations": active_operations,
            "recommendations": _generate_recommendations(degradation_status, is_healthy, operation_metrics)
        }
        
        return system_status
        
    except Exception as e:
        logging.error(f"Error getting system health status: {str(e)}")
        return {
            "timestamp": datetime.now().isoformat(),
            "overall_health": "error",
            "error": str(e),
            "recommendations": ["Check system logs for detailed error information"]
        }


def _generate_recommendations(degradation_status: Dict[str, Any], is_healthy: bool, operation_metrics: Dict[str, Any] = None) -> list:
    """Generate recommendations based on current system status and operation metrics."""
    recommendations = []
    
    if degradation_status['degraded_mode']:
        recommendations.append("System is in degraded mode - vector store connectivity issues detected")
        recommendations.append(f"Reason: {degradation_status['degradation_reason']}")
        
        if degradation_status['backlog_size'] > 0:
            recommendations.append(f"There are {degradation_status['backlog_size']} operations queued for processing")
            recommendations.append("Operations will be processed automatically when vector store connectivity is restored")
        
        if degradation_status['recovery_attempts'] > 0:
            recommendations.append(f"Recovery attempts: {degradation_status['recovery_attempts']}/{degradation_status['max_recovery_attempts']}")
            
            if degradation_status['recovery_attempts'] >= degradation_status['max_recovery_attempts']:
                recommendations.append("Maximum recovery attempts reached - system will retry after cooldown period")
                if degradation_status['last_recovery_attempt']:
                    next_attempt_time = degradation_status['last_recovery_attempt'] + 300  # 5 minutes cooldown
                    recommendations.append(f"Next recovery attempt will be after {datetime.fromtimestamp(next_attempt_time).isoformat()}")
        
        recommendations.append("Check vector store (Qdrant) connectivity and configuration")
        recommendations.append("Verify network connectivity to vector store service")
    
    elif not is_healthy:
        recommendations.append("Memory client is not healthy - check system configuration")
        recommendations.append("Review application logs for initialization errors")
    
    else:
        recommendations.append("System is operating normally")
        recommendations.append("All memory operations are functioning as expected")
    
    # Add operation metrics-based recommendations
    if operation_metrics and not operation_metrics.get('error'):
        # Check for high error rates
        for operation, metrics in operation_metrics.get('operations', {}).items():
            if metrics.get('total_operations', 0) > 0:
                error_rate = (metrics.get('failed_operations', 0) / metrics.get('total_operations', 1)) * 100
                if error_rate > 10:  # More than 10% error rate
                    recommendations.append(f"High error rate detected for {operation}: {error_rate:.1f}%")
                    recommendations.append(f"Review logs for {operation} operation failures")
        
        # Check for slow operations
        for operation, metrics in operation_metrics.get('operations', {}).items():
            avg_duration = metrics.get('average_duration_ms', 0)
            if avg_duration > 5000:  # More than 5 seconds average
                recommendations.append(f"Slow performance detected for {operation}: {avg_duration:.0f}ms average")
                recommendations.append(f"Consider optimizing {operation} operation or checking system resources")
        
        # Check for active operations that might be stuck
        active_count = operation_metrics.get('active_operations_count', 0)
        if active_count > 10:
            recommendations.append(f"High number of active operations: {active_count}")
            recommendations.append("Monitor for potential bottlenecks or stuck operations")
    
    return recommendations


def format_status_for_display(status: Dict[str, Any]) -> str:
    """Format system status for human-readable display."""
    try:
        output = []
        output.append(f"=== OpenMemory System Health Status ===")
        output.append(f"Timestamp: {status['timestamp']}")
        output.append(f"Overall Health: {status['overall_health'].upper()}")
        output.append("")
        
        # Degradation status
        degradation = status.get('degradation', {})
        if degradation.get('degraded_mode', False):
            output.append("🔴 DEGRADED MODE ACTIVE")
            output.append(f"   Reason: {degradation.get('degradation_reason', 'Unknown')}")
            output.append(f"   Backlog Size: {degradation.get('backlog_size', 0)} operations")
            output.append(f"   Recovery Attempts: {degradation.get('recovery_attempts', 0)}/{degradation.get('max_recovery_attempts', 3)}")
        else:
            output.append("🟢 Normal Operation")
        
        output.append("")
        
        # Operation queue status
        queue_status = status.get('operation_queue', {})
        if queue_status:
            output.append(f"Operation Queue: {queue_status.get('size', 0)} pending operations")
            if queue_status.get('worker_running', False):
                output.append("   Worker Status: Running")
            else:
                output.append("   Worker Status: Stopped")
        
        output.append("")
        
        # Operation metrics
        operation_metrics = status.get('operation_metrics', {})
        if operation_metrics and not operation_metrics.get('error'):
            output.append("📊 Operation Metrics:")
            for operation, metrics in operation_metrics.get('operations', {}).items():
                total = metrics.get('total_operations', 0)
                failed = metrics.get('failed_operations', 0)
                avg_duration = metrics.get('average_duration_ms', 0)
                success_rate = ((total - failed) / total * 100) if total > 0 else 0
                output.append(f"   {operation}: {total} ops, {success_rate:.1f}% success, {avg_duration:.0f}ms avg")
            
            active_ops = status.get('active_operations', {})
            if active_ops:
                output.append(f"   Active Operations: {len(active_ops)}")
        elif operation_metrics and operation_metrics.get('error'):
            output.append(f"⚠️  Operation Metrics: {operation_metrics['error']}")
        
        output.append("")
        
        # Recommendations
        recommendations = status.get('recommendations', [])
        if recommendations:
            output.append("Recommendations:")
            for i, rec in enumerate(recommendations, 1):
                output.append(f"  {i}. {rec}")
        
        return "\n".join(output)
        
    except Exception as e:
        return f"Error formatting status: {str(e)}\n\nRaw status: {json.dumps(status, indent=2)}"