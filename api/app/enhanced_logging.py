#!/usr/bin/env python3
"""
Enhanced Error Logging and Status Reporting for Memory Operations.

This module provides comprehensive error logging, status reporting, and operation
tracking for memory operations in the OpenMemory system. It includes structured
logging, timing metrics, error classification, and operation result tracking.

Key features:
- Structured logging with operation context
- Comprehensive error classification
- Operation timing and performance metrics
- Status reporting with detailed operation results
- Transaction-like operation tracking
"""

import logging
import time
import functools
import uuid
import datetime
from enum import Enum
from typing import Dict, Any, Optional, List


class MemoryOperationStatus(Enum):
    """Enumeration of possible memory operation statuses."""
    SUCCESS = "success"
    PARTIAL_SUCCESS = "partial_success"
    FAILURE = "failure"
    DEGRADED = "degraded"
    TIMEOUT = "timeout"
    VALIDATION_ERROR = "validation_error"
    RETRY_EXHAUSTED = "retry_exhausted"
    CONNECTION_ERROR = "connection_error"
    AUTHENTICATION_ERROR = "authentication_error"


class MemoryOperationResult:
    """Comprehensive result object for memory operations with timing and status information."""
    
    def __init__(self, status: MemoryOperationStatus, message: str, data: Any = None, 
                 duration_ms: Optional[int] = None, operation_type: str = "unknown",
                 user_id: str = None, client_name: str = None, error_details: Dict = None,
                 retry_count: int = 0, validation_errors: List[str] = None):
        self.status = status
        self.message = message
        self.data = data or {}
        self.duration_ms = duration_ms
        self.operation_type = operation_type
        self.user_id = user_id
        self.client_name = client_name
        self.error_details = error_details or {}
        self.retry_count = retry_count
        self.validation_errors = validation_errors or []
        self.timestamp = time.time()
        self.operation_id = str(uuid.uuid4())
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert operation result to dictionary for logging and serialization."""
        return {
            "operation_id": self.operation_id,
            "status": self.status.value,
            "message": self.message,
            "operation_type": self.operation_type,
            "data": self.data,
            "duration_ms": self.duration_ms,
            "timestamp": self.timestamp,
            "iso_timestamp": datetime.datetime.fromtimestamp(self.timestamp).isoformat(),
            "user_id": self.user_id,
            "client_name": self.client_name,
            "error_details": self.error_details,
            "retry_count": self.retry_count,
            "validation_errors": self.validation_errors
        }
    
    def is_success(self) -> bool:
        """Check if operation was successful."""
        return self.status in [MemoryOperationStatus.SUCCESS, MemoryOperationStatus.PARTIAL_SUCCESS]
    
    def is_failure(self) -> bool:
        """Check if operation failed."""
        return not self.is_success()
    
    def is_retryable(self) -> bool:
        """Check if operation failure is retryable."""
        retryable_statuses = [
            MemoryOperationStatus.TIMEOUT,
            MemoryOperationStatus.CONNECTION_ERROR,
            MemoryOperationStatus.DEGRADED
        ]
        return self.status in retryable_statuses


class MemoryOperationLogger:
    """Enhanced logger for memory operations with structured logging and metrics."""
    
    def __init__(self, logger_name: str = "memory_operations"):
        self.logger = logging.getLogger(logger_name)
        self.operation_metrics = {}
        self.active_operations = {}  # Track ongoing operations
        
        # Configure structured logging format
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
        
    def log_operation_start(self, operation_type: str, user_id: str = None, 
                          client_name: str = None, details: Dict = None) -> str:
        """Log the start of a memory operation and return operation ID."""
        operation_id = str(uuid.uuid4())
        start_time = time.time()
        
        log_data = {
            "event": "operation_start",
            "operation_id": operation_id,
            "operation_type": operation_type,
            "user_id": user_id,
            "client_name": client_name,
            "timestamp": start_time,
            "iso_timestamp": datetime.datetime.fromtimestamp(start_time).isoformat(),
            "details": details or {}
        }
        
        # Store active operation
        self.active_operations[operation_id] = {
            "operation_type": operation_type,
            "start_time": start_time,
            "user_id": user_id,
            "client_name": client_name
        }
        
        self.logger.info(
            f"Starting {operation_type} [ID: {operation_id[:8]}]",
            extra={"structured_data": log_data}
        )
        return operation_id
    
    def log_operation_result(self, result: MemoryOperationResult):
        """Log the result of a memory operation with full context."""
        log_level = logging.INFO if result.is_success() else logging.ERROR
        
        # Create structured log data
        log_data = result.to_dict()
        log_data["event"] = "operation_complete"
        
        # Add performance indicators
        if result.duration_ms is not None:
            log_data["performance"] = self._classify_performance(result.operation_type, result.duration_ms)
        
        # Log with appropriate level and context
        message = f"{result.operation_type}: {result.message}"
        if result.duration_ms is not None:
            message += f" ({result.duration_ms}ms)"
        if result.retry_count > 0:
            message += f" [retries: {result.retry_count}]"
            
        self.logger.log(log_level, message, extra={"structured_data": log_data})
        
        # Clean up active operation tracking
        if result.operation_id in self.active_operations:
            del self.active_operations[result.operation_id]
        
        # Update metrics
        self._update_metrics(result)
    
    def log_operation_retry(self, operation_id: str, attempt: int, error: str, next_attempt_delay: float = None):
        """Log retry attempts for operations."""
        log_data = {
            "event": "operation_retry",
            "operation_id": operation_id,
            "attempt": attempt,
            "error": error,
            "next_attempt_delay": next_attempt_delay,
            "timestamp": time.time()
        }
        
        message = f"Retry attempt {attempt} for operation {operation_id[:8]}: {error}"
        if next_attempt_delay:
            message += f" (next attempt in {next_attempt_delay:.2f}s)"
            
        self.logger.warning(message, extra={"structured_data": log_data})
    
    def _classify_performance(self, operation_type: str, duration_ms: int) -> str:
        """Classify operation performance based on duration."""
        # Define performance thresholds by operation type
        thresholds = {
            "add_memory": {"fast": 500, "normal": 2000, "slow": 5000},
            "search_memories": {"fast": 200, "normal": 1000, "slow": 3000},
            "get_memories": {"fast": 100, "normal": 500, "slow": 1500},
            "update_memory": {"fast": 300, "normal": 1500, "slow": 4000},
            "delete_memory": {"fast": 200, "normal": 1000, "slow": 3000}
        }
        
        # Use default thresholds if operation type not found
        default_thresholds = {"fast": 300, "normal": 1500, "slow": 4000}
        op_thresholds = thresholds.get(operation_type, default_thresholds)
        
        if duration_ms <= op_thresholds["fast"]:
            return "fast"
        elif duration_ms <= op_thresholds["normal"]:
            return "normal"
        elif duration_ms <= op_thresholds["slow"]:
            return "slow"
        else:
            return "very_slow"
    
    def _update_metrics(self, result: MemoryOperationResult):
        """Update operation metrics for monitoring."""
        op_type = result.operation_type
        if op_type not in self.operation_metrics:
            self.operation_metrics[op_type] = {
                "total_count": 0,
                "success_count": 0,
                "failure_count": 0,
                "retry_count": 0,
                "total_duration_ms": 0,
                "avg_duration_ms": 0,
                "min_duration_ms": None,
                "max_duration_ms": None,
                "last_operation": None,
                "error_types": {},
                "performance_distribution": {"fast": 0, "normal": 0, "slow": 0, "very_slow": 0}
            }
        
        metrics = self.operation_metrics[op_type]
        metrics["total_count"] += 1
        metrics["retry_count"] += result.retry_count
        metrics["last_operation"] = result.timestamp
        
        if result.is_success():
            metrics["success_count"] += 1
        else:
            metrics["failure_count"] += 1
            # Track error types
            error_type = result.status.value
            metrics["error_types"][error_type] = metrics["error_types"].get(error_type, 0) + 1
            
        if result.duration_ms is not None:
            metrics["total_duration_ms"] += result.duration_ms
            metrics["avg_duration_ms"] = metrics["total_duration_ms"] / metrics["total_count"]
            
            # Update min/max duration
            if metrics["min_duration_ms"] is None or result.duration_ms < metrics["min_duration_ms"]:
                metrics["min_duration_ms"] = result.duration_ms
            if metrics["max_duration_ms"] is None or result.duration_ms > metrics["max_duration_ms"]:
                metrics["max_duration_ms"] = result.duration_ms
            
            # Update performance distribution
            performance = self._classify_performance(op_type, result.duration_ms)
            metrics["performance_distribution"][performance] += 1
    
    def get_operation_metrics(self) -> Dict[str, Any]:
        """Get current operation metrics for monitoring."""
        return {
            "metrics_timestamp": time.time(),
            "active_operations_count": len(self.active_operations),
            "active_operations": list(self.active_operations.keys()),
            "operations": self.operation_metrics.copy()
        }
    
    def get_active_operations(self) -> Dict[str, Any]:
        """Get information about currently active operations."""
        current_time = time.time()
        active_ops = {}
        
        for op_id, op_info in self.active_operations.items():
            duration_ms = int((current_time - op_info["start_time"]) * 1000)
            active_ops[op_id] = {
                **op_info,
                "duration_ms": duration_ms,
                "is_long_running": duration_ms > 10000  # 10 seconds
            }
        
        return active_ops


def classify_error(exception: Exception) -> MemoryOperationStatus:
    """Classify exceptions into appropriate operation status categories."""
    exception_type = type(exception).__name__
    exception_message = str(exception).lower()
    
    # Timeout-related errors
    if "timeout" in exception_message or exception_type in ["TimeoutError", "ConnectTimeout", "ReadTimeout"]:
        return MemoryOperationStatus.TIMEOUT
    
    # Authentication/authorization errors
    if any(keyword in exception_message for keyword in ["unauthorized", "forbidden", "authentication", "api key"]):
        return MemoryOperationStatus.AUTHENTICATION_ERROR
    
    # Validation errors
    if "validation" in exception_message or exception_type in ["ValidationError", "ValueError"]:
        return MemoryOperationStatus.VALIDATION_ERROR
    
    # Connection/network errors that might indicate degraded mode
    if any(keyword in exception_message for keyword in ["connection", "network", "unreachable", "refused", "dns"]):
        return MemoryOperationStatus.CONNECTION_ERROR
    
    # Vector store specific errors that indicate degraded mode
    if any(keyword in exception_message for keyword in ["qdrant", "vector store", "embedding"]):
        return MemoryOperationStatus.DEGRADED
    
    # Default to general failure
    return MemoryOperationStatus.FAILURE


def create_operation_result(status: MemoryOperationStatus, message: str, 
                          operation_type: str, data: Any = None, 
                          duration_ms: int = None, user_id: str = None, 
                          client_name: str = None, error_details: Dict = None,
                          retry_count: int = 0) -> MemoryOperationResult:
    """Factory function to create standardized operation results."""
    return MemoryOperationResult(
        status=status,
        message=message,
        operation_type=operation_type,
        data=data,
        duration_ms=duration_ms,
        user_id=user_id,
        client_name=client_name,
        error_details=error_details,
        retry_count=retry_count
    )


# Global operation logger instance
operation_logger = MemoryOperationLogger()


def log_memory_operation(operation_name: str):
    """Decorator to log memory operations with comprehensive timing and status tracking."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Import context variables here to avoid circular imports
            try:
                from app.mcp_server import user_id_var, client_name_var
                user_id = user_id_var.get(None)
                client_name = client_name_var.get(None)
            except (ImportError, LookupError):
                user_id = None
                client_name = None
            
            # Start operation logging
            operation_id = operation_logger.log_operation_start(
                operation_name, user_id, client_name, 
                {"args_count": len(args), "kwargs_keys": list(kwargs.keys())}
            )
            
            start_time = time.time()
            result = None
            
            try:
                # Execute the operation
                result = func(*args, **kwargs)
                end_time = time.time()
                duration_ms = int((end_time - start_time) * 1000)
                
                # Import validation function here to avoid circular imports
                try:
                    from app.mcp_server import validate_mem0_response
                    success, message = validate_mem0_response(result, operation_name)
                except ImportError:
                    # Fallback validation if import fails
                    success = result is not None
                    message = f"{operation_name} completed" if success else f"{operation_name} returned None"
                
                status = MemoryOperationStatus.SUCCESS if success else MemoryOperationStatus.VALIDATION_ERROR
                
                # Create operation result
                op_result = create_operation_result(
                    status=status,
                    message=message,
                    operation_type=operation_name,
                    data=result,
                    duration_ms=duration_ms,
                    user_id=user_id,
                    client_name=client_name
                )
                
                # Log the result
                operation_logger.log_operation_result(op_result)
                
                return result
                
            except Exception as e:
                end_time = time.time()
                duration_ms = int((end_time - start_time) * 1000)
                
                # Classify the error
                error_status = classify_error(e)
                error_details = {
                    "exception_type": type(e).__name__,
                    "exception_message": str(e),
                    "operation_id": operation_id
                }
                
                # Create failure result
                op_result = create_operation_result(
                    status=error_status,
                    message=f"{operation_name} failed: {str(e)}",
                    operation_type=operation_name,
                    data={"error": str(e)},
                    duration_ms=duration_ms,
                    user_id=user_id,
                    client_name=client_name,
                    error_details=error_details
                )
                
                # Log the failure
                operation_logger.log_operation_result(op_result)
                
                # Re-raise the exception
                raise
                
        return wrapper
    return decorator