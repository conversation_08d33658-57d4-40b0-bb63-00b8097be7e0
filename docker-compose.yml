services:
  mem0_store:
    image: qdrant/qdrant
    container_name: memory-qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"
    volumes:
      - mem0_storage:/qdrant/storage
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
  openmemory-mcp:
    image: mem0/openmemory-mcp
    container_name: memory-mcp
    restart: unless-stopped
    build: api/
    environment:
      - USER
      - API_KEY
      - DATABASE_URL=***********************************************************************************
      - MIGRATION_MODE=supabase_only
    env_file:
      - api/.env
    depends_on:
      - mem0_store
    ports:
      - "8765:8765"
    volumes:
      - ./api:/usr/src/openmemory
    command: >
      sh -c "uvicorn main:app --host 0.0.0.0 --port 8765 --reload --workers 4"
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
  openmemory-ui:
    build:
      context: ui/
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
        - NEXT_PUBLIC_USER_ID=${USER}
        - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
        - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
        - NEXT_PUBLIC_AUTH_ENABLED=${NEXT_PUBLIC_AUTH_ENABLED}
    image: mem0/openmemory-ui:latest
    container_name: memory-ui
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env
    environment:
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - NEXT_PUBLIC_USER_ID=${USER}
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - NEXT_PUBLIC_AUTH_ENABLED=${NEXT_PUBLIC_AUTH_ENABLED}
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

    # MinIO client for backup operations to remote MinIO
  minio-client:
    image: minio/mc:latest
    container_name: memory-backup-storage
    restart: unless-stopped
    volumes:
      - ./backup-scripts:/scripts
      - /var/run/docker.sock:/var/run/docker.sock
      - ./api:/api-data  # Mount for accessing SQLite files
    environment:
      - MINIO_ENDPOINT=http://*************:9000
      - MINIO_CONSOLE=http://*************:9090
    entrypoint: ["tail", "-f", "/dev/null"]  # Keep container running

volumes:
  mem0_storage:

networks:
  default:
    name: memory-network