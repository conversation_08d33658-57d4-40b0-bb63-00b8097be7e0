'use client'

import React from 'react'
import { useAuth } from '@/lib/auth/AuthProvider'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LoginForm } from './LoginForm'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  requireAuth?: boolean
}

export function ProtectedRoute({
  children,
  fallback,
  requireAuth = true
}: ProtectedRouteProps) {
  const { user, loading } = useAuth()

  // Check if authentication is enabled via environment variable
  const isAuthEnabled = process.env.NEXT_PUBLIC_AUTH_ENABLED === 'true'

  // If auth is disabled, always show children
  if (!isAuthEnabled || !requireAuth) {
    return <>{children}</>
  }

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  // Show login form if not authenticated
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[50vh] p-4">
        {fallback || (
          <div className="w-full max-w-md">
            <Card className="mb-4">
              <CardHeader className="text-center">
                <CardTitle>Authentication Required</CardTitle>
                <CardDescription>
                  Please sign in to access this feature
                </CardDescription>
              </CardHeader>
            </Card>
            <LoginForm />
          </div>
        )}
      </div>
    )
  }

  // User is authenticated, show protected content
  return <>{children}</>
}