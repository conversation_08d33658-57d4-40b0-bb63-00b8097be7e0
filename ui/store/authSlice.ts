import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'

interface User {
  user_id: string
  email: string
  name?: string
  email_verified: boolean
  is_authenticated: boolean
  supabase_user_id?: string
  created_at?: string
  last_sign_in_at?: string
}

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  authEnabled: boolean
  loading: boolean
  error: string | null
  initialized: boolean
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  authEnabled: false,
  loading: false,
  error: null,
  initialized: false
}

// Async thunks
export const fetchAuthStatus = createAsyncThunk(
  'auth/fetchStatus',
  async (_, { rejectWithValue }) => {
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
      const token = localStorage.getItem('supabase.auth.token')
      const headers: HeadersInit = token ? { Authorization: `Bearer ${token}` } : {}
      
      const response = await fetch(`${API_BASE_URL}/auth/status`, { headers })
      
      if (!response.ok) {
        throw new Error('Failed to fetch auth status')
      }
      
      return await response.json()
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const updateUserProfile = createAsyncThunk(
  'auth/updateProfile',
  async (data: { name?: string }, { rejectWithValue }) => {
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
      const token = localStorage.getItem('supabase.auth.token')
      
      if (!token) {
        throw new Error('No authentication token found')
      }
      
      const response = await fetch(`${API_BASE_URL}/auth/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify(data)
      })
      
      if (!response.ok) {
        throw new Error('Failed to update profile')
      }
      
      return await response.json()
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setAuthState: (state, action: PayloadAction<{ user: User | null; isAuthenticated: boolean; authEnabled: boolean }>) => {
      state.user = action.payload.user
      state.isAuthenticated = action.payload.isAuthenticated
      state.authEnabled = action.payload.authEnabled
      state.initialized = true
      state.error = null
    },
    clearAuth: (state) => {
      state.user = null
      state.isAuthenticated = false
      state.error = null
    },
    setAuthError: (state, action: PayloadAction<string>) => {
      state.error = action.payload
      state.loading = false
    },
    clearAuthError: (state) => {
      state.error = null
    },
    setAuthLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload
    }
  },
  extraReducers: (builder) => {
    builder
      // fetchAuthStatus
      .addCase(fetchAuthStatus.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchAuthStatus.fulfilled, (state, action) => {
        state.loading = false
        state.user = action.payload.user
        state.isAuthenticated = action.payload.is_authenticated
        state.authEnabled = action.payload.auth_enabled
        state.initialized = true
        state.error = null
      })
      .addCase(fetchAuthStatus.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
        state.initialized = true
      })
      // updateUserProfile
      .addCase(updateUserProfile.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.loading = false
        state.user = action.payload
        state.error = null
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  }
})

export const { setAuthState, clearAuth, setAuthError, clearAuthError, setAuthLoading } = authSlice.actions
export default authSlice.reducer