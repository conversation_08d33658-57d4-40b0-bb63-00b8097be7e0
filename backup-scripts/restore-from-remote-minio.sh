#!/bin/bash
# safe-restore-from-remote-minio.sh - Safe restore script with confirmations

set -e

BACKUP_FILE=${1:-latest-backup.tar.gz}
LOG_FILE="/home/<USER>/memory-master-v2/logs/restore.log"
BUCKET_NAME="memory-master-dev-backups"

# Function to prompt for confirmation
confirm_action() {
    local message=$1
    echo "⚠️  $message"
    read -p "Type 'YES' to confirm (anything else cancels): " confirmation
    if [ "$confirmation" != "YES" ]; then
        echo "❌ Operation cancelled by user"
        exit 1
    fi
}

# Log restore operation
echo "$(date): 🔄 SAFE RESTORE OPERATION INITIATED" | tee -a $LOG_FILE
echo "$(date): Backup file: $BACKUP_FILE" | tee -a $LOG_FILE

# Check if this is being run interactively
if [ ! -t 0 ]; then
    echo "$(date): ❌ ERROR: This script cannot run non-interactively for safety!" | tee -a $LOG_FILE
    echo "$(date): To prevent accidental data loss, this script requires manual confirmation." | tee -a $LOG_FILE
    exit 1
fi

# Warning and confirmations
echo ""
echo "🚨 CRITICAL WARNING: This restore operation will:"
echo "   1. COMPLETELY WIPE all Qdrant collections"
echo "   2. Replace the SQLite database"
echo "   3. Stop all services temporarily"
echo ""

confirm_action "This will PERMANENTLY DELETE all current Qdrant vector data!"

echo ""
echo "📋 Pre-restore checklist:"
echo "   ✓ Current data will be backed up before restore"
echo "   ✓ Recovery script is available if needed"
echo ""

confirm_action "Proceed with the dangerous restore operation?"

# Create emergency backup before restore
echo "$(date): Creating emergency backup before restore..." | tee -a $LOG_FILE
EMERGENCY_BACKUP_DIR="/tmp/emergency_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p $EMERGENCY_BACKUP_DIR/{sqlite,qdrant}

# Backup current SQLite
docker cp memory-mcp:/usr/src/openmemory/openmemory.db $EMERGENCY_BACKUP_DIR/sqlite/openmemory.db 2>/dev/null || echo "No current SQLite to backup"

# Backup current Qdrant
docker run --rm -v mem0_storage:/source -v $EMERGENCY_BACKUP_DIR/qdrant:/backup \
  alpine tar czf /backup/qdrant_data.tar.gz -C /source . 2>/dev/null || echo "No current Qdrant data to backup"

echo "$(date): Emergency backup created at: $EMERGENCY_BACKUP_DIR" | tee -a $LOG_FILE

# Download and extract
echo "$(date): Downloading backup from MinIO..." | tee -a $LOG_FILE
docker exec memory-backup-storage mc cp remoteminio/$BUCKET_NAME/$BACKUP_FILE /tmp/
docker cp memory-backup-storage:/tmp/$BACKUP_FILE ./

# Stop, restore, start
echo "$(date): Stopping services..." | tee -a $LOG_FILE
docker-compose down
tar xzf $BACKUP_FILE -C /tmp/

echo "$(date): CRITICAL - Removing Qdrant volume (THIS WIPES ALL COLLECTIONS)" | tee -a $LOG_FILE
docker volume rm mem0_storage && docker volume create mem0_storage

echo "$(date): Restoring Qdrant data from backup..." | tee -a $LOG_FILE
docker run --rm -v mem0_storage:/target -v /tmp/backup_*/qdrant:/source alpine tar xzf /source/qdrant_data.tar.gz -C /target

# Restore config files
cp /tmp/backup_*/config/.env ./api/ 2>/dev/null || true
cp /tmp/backup_*/config/config.json ./api/ 2>/dev/null || true

echo "$(date): Starting services..." | tee -a $LOG_FILE
docker-compose up -d
sleep 15

echo "$(date): Restoring SQLite database..." | tee -a $LOG_FILE
docker cp /tmp/backup_*/sqlite/openmemory.db memory-mcp:/usr/src/openmemory/
docker-compose restart memory-mcp

# Cleanup
rm -rf /tmp/backup_* $BACKUP_FILE
docker exec memory-backup-storage rm /tmp/$BACKUP_FILE

echo "$(date): ✅ Safe restore completed!" | tee -a $LOG_FILE
echo "$(date): Emergency backup available at: $EMERGENCY_BACKUP_DIR" | tee -a $LOG_FILE
echo ""
echo "🎉 Restore completed successfully!"
echo "📁 Emergency backup: $EMERGENCY_BACKUP_DIR"
echo "🔧 Recovery tool: ./backup-scripts/recover-qdrant-from-sqlite.sh"