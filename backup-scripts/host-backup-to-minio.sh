#!/bin/bash
# host-backup-to-minio.sh - Host-based backup script

set -e

BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
TEMP_BACKUP_DIR="/tmp/backup_$BACKUP_DATE"
# Modify this dev or ops
BUCKET_NAME="memory-master-dev-backups"
MINIO_ALIAS="remoteminio"

echo "Starting Memory-Master-v2 HOT backup to remote MinIO: $BACKUP_DATE"
echo "📊 Backup started - System status:"
echo "Memory usage: $(docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}" memory-mcp)"
echo "CPU usage: $(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}" memory-mcp)"


# Create temporary backup directory on host
mkdir -p $TEMP_BACKUP_DIR/{sqlite,qdrant,config}

# 1. Create SQLite backup using Python script inside container
echo "Creating SQLite backup using Python..."
docker exec memory-mcp python3 -c "
import sqlite3
import shutil
import os

# Source and destination paths
src_db = '/usr/src/openmemory/openmemory.db'
backup_db = '/usr/src/openmemory/backup_hot.db'

# Remove old backup if exists
if os.path.exists(backup_db):
    os.remove(backup_db)

# Create backup using SQLite backup API (hot backup)
try:
    src_conn = sqlite3.connect(src_db)
    backup_conn = sqlite3.connect(backup_db)
    src_conn.backup(backup_conn)
    backup_conn.close()
    src_conn.close()
    print('✅ SQLite hot backup created successfully')
except Exception as e:
    print(f'❌ SQLite backup failed: {e}')
    exit(1)
"

# Copy the backup file from container
docker cp memory-mcp:/usr/src/openmemory/backup_hot.db $TEMP_BACKUP_DIR/sqlite/openmemory.db

# Clean up the temporary backup file in container
docker exec memory-mcp rm /usr/src/openmemory/backup_hot.db

# 2. Backup Qdrant Vector Data
echo "Backing up Qdrant vector data..."
docker run --rm -v mem0_storage:/source -v $TEMP_BACKUP_DIR/qdrant:/backup \
  alpine tar czf /backup/qdrant_data.tar.gz -C /source .

# 3. Backup Configuration Files
echo "Backing up configuration..."
cp api/.env $TEMP_BACKUP_DIR/config/ 2>/dev/null || echo "No .env file found"
cp api/config.json $TEMP_BACKUP_DIR/config/ 2>/dev/null || echo "No config.json found"
cp docker-compose.yml $TEMP_BACKUP_DIR/config/

# 4. Create backup manifest
echo "Creating backup manifest..."
cat > $TEMP_BACKUP_DIR/backup_manifest.json << EOF
{
  "backup_date": "$BACKUP_DATE",
  "backup_type": "hot_backup",
  "backup_version": "1.0",
  "backup_method": "python_sqlite_backup_api",
  "backup_destination": "remote_minio",
  "minio_endpoint": "http://192.168.1.177:9090",
  "minio_console": "http://192.168.1.177:9091",
  "bucket_name": "$BUCKET_NAME",
  "components": {
    "sqlite_db": "sqlite/openmemory.db",
    "qdrant_data": "qdrant/qdrant_data.tar.gz",
    "configuration": "config/"
  },
  "docker_images": {
    "qdrant_version": "$(docker inspect --format='{{index .RepoTags 0}}' qdrant/qdrant 2>/dev/null || echo 'unknown')",
    "api_image": "$(docker inspect --format='{{.Config.Image}}' memory-mcp 2>/dev/null || echo 'unknown')"
  },
  "backup_size_mb": "$(du -sm $TEMP_BACKUP_DIR | cut -f1)",
  "service_interruption": "none"
}
EOF

# 5. Compress backup
echo "Compressing backup..."
cd $(dirname $TEMP_BACKUP_DIR)
tar czf "memory-master-backup-$BACKUP_DATE.tar.gz" $(basename $TEMP_BACKUP_DIR)/

# 6. Copy backup file to MinIO client container
echo "Copying backup to MinIO client container..."
docker cp "memory-master-backup-$BACKUP_DATE.tar.gz" memory-backup-storage:/tmp/

# 7. Upload via MinIO client container
echo "Uploading to remote MinIO at 192.168.1.177..."
docker exec memory-backup-storage mc cp /tmp/memory-master-backup-$BACKUP_DATE.tar.gz $MINIO_ALIAS/$BUCKET_NAME/

# 8. Verify upload
echo "Verifying upload..."
REMOTE_INFO=$(docker exec memory-backup-storage mc stat $MINIO_ALIAS/$BUCKET_NAME/memory-master-backup-$BACKUP_DATE.tar.gz --json)
UPLOAD_SIZE=$(echo $REMOTE_INFO | jq -r '.size')
LOCAL_SIZE=$(stat -c%s "memory-master-backup-$BACKUP_DATE.tar.gz")

if [ "$UPLOAD_SIZE" = "$LOCAL_SIZE" ]; then
    echo "✅ HOT backup successfully uploaded to remote MinIO: memory-master-backup-$BACKUP_DATE.tar.gz"
    echo "📊 Backup size: $(( $LOCAL_SIZE / 1024 / 1024 )) MB"
    
    # Update latest backup reference
    docker exec memory-backup-storage mc cp $MINIO_ALIAS/$BUCKET_NAME/memory-master-backup-$BACKUP_DATE.tar.gz \
      $MINIO_ALIAS/$BUCKET_NAME/latest-backup.tar.gz
    
    echo "✅ Latest backup reference updated"
    echo "🌐 Access via MinIO Console: http://192.168.1.177:9091"
    
    # Cleanup local files
    rm -rf $TEMP_BACKUP_DIR
    rm "memory-master-backup-$BACKUP_DATE.tar.gz"
    
    # Cleanup container temp file
    docker exec memory-backup-storage rm /tmp/memory-master-backup-$BACKUP_DATE.tar.gz
    
else
    echo "❌ Upload verification failed!"
    echo "Local size: $LOCAL_SIZE bytes"
    echo "Remote size: $UPLOAD_SIZE bytes"
    exit 1
fi

echo "HOT backup completed: $BACKUP_DATE (no service interruption)"
echo "Remote location: $MINIO_ALIAS/$BUCKET_NAME/memory-master-backup-$BACKUP_DATE.tar.gz"
echo "📊 Backup completed - System status:"
echo "Memory usage: $(docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}" memory-mcp)"
echo "CPU usage: $(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}" memory-mcp)"